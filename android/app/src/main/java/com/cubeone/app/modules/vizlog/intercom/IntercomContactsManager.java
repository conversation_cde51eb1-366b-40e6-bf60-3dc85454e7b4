package com.cubeone.app.modules.vizlog.intercom;

import android.content.Context;

import com.tomash.androidcontacts.contactgetter.entity.ContactData;
import com.tomash.androidcontacts.contactgetter.entity.NameData;
import com.tomash.androidcontacts.contactgetter.entity.PhoneNumber;
import com.tomash.androidcontacts.contactgetter.main.ContactDataFactory;
import com.tomash.androidcontacts.contactgetter.main.FieldType;
import com.tomash.androidcontacts.contactgetter.main.contactsDeleter.ContactsDeleter;
import com.tomash.androidcontacts.contactgetter.main.contactsGetter.ContactsGetterBuilder;
import com.tomash.androidcontacts.contactgetter.main.contactsSaver.ContactsSaverBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import kotlin.Unit;

public class IntercomContactsManager {

    private static final String TAG = "IntercomContactsManager";
    private static IntercomContactsManager mInstance;

    private IntercomContactsManager() {}

    public static synchronized IntercomContactsManager getInstance() {
        if (mInstance == null) {
            mInstance = new IntercomContactsManager();
        }
        return mInstance;
    }

    public List<ContactData> getContactsWithPhoneNumber(Context context,
                                                           String phoneNumber) throws Exception {
        if(!PermissionUtils.hasReadContactsPermission(context)) {
            LogUtils.printReadContactsPermissionError(TAG);
            throw new Exception("Read Contacts Permission not granted");
        }

        if(phoneNumber == null || phoneNumber.trim().isEmpty()) {
            throw new IllegalArgumentException("Phone Number must not be null or empty");
        }

        List<ContactData> contactDataList = new ContactsGetterBuilder(context)
                .addField(FieldType.NAME_DATA, FieldType.PHONE_NUMBERS)
                .withPhone(phoneNumber)
                .buildList();
        LogUtils.printContactList(TAG, contactDataList);

        return contactDataList;
    }

    public ContactData getFirstContactWithPhoneNumber(Context context,
                                                      String phoneNumber) throws Exception {
        if(!PermissionUtils.hasReadContactsPermission(context)) {
            LogUtils.printReadContactsPermissionError(TAG);
            throw new Exception("Read Contacts Permission not granted");
        }

        if(phoneNumber == null || phoneNumber.trim().isEmpty()) {
            throw new IllegalArgumentException("Phone Number must not be null or empty");
        }

        ContactData contactData = new ContactsGetterBuilder(context)
                .addField(FieldType.NAME_DATA, FieldType.PHONE_NUMBERS)
                .withPhone(phoneNumber)
                .firstOrNull();
        LogUtils.printContact(TAG, contactData);

        return contactData;
    }

    public int addNewContact(Context context, String name,
                             String phoneNumber) throws Exception {
        if(!PermissionUtils.hasWriteContactsPermission(context)) {
            LogUtils.printWriteContactsPermissionError(TAG);
            throw new Exception("Write Contacts Permission not granted");
        }

        if(name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Name must not be null or empty");
        }

        if(phoneNumber == null || phoneNumber.trim().isEmpty()) {
            throw new IllegalArgumentException("Phone Number must not be null or empty");
        }

        ContactData data = ContactDataFactory.createEmpty();
        data.setNameData(new NameData()
                .setFullName(name));

        List<PhoneNumber> phoneNumberList = new ArrayList<>();
        phoneNumberList.add(new PhoneNumber(context, phoneNumber));
        data.setPhoneList(phoneNumberList);

        return new ContactsSaverBuilder(context)
                .saveContact(data);
    }

    public void deleteContactWithPhoneNumber(Context context,
                                              String phoneNumber,
                                              OnContactDeleteListener listener) throws Exception {
        if(!PermissionUtils.hasWriteContactsPermission(context)) {
            LogUtils.printWriteContactsPermissionError(TAG);
            throw new Exception("Write Contacts Permission not granted");
        }

        ContactData contactData = getFirstContactWithPhoneNumber(context, phoneNumber);
        if (contactData != null) {
            deleteContact(context, contactData, listener);
        } else {
            if (listener != null) {
                listener.onNothingToDelete();
            }
        }
    }

    public void deleteContactsWithPhoneNumber(Context context,
                                              String phoneNumber,
                                              OnContactsDeleteListener listener) throws Exception {
        if(!PermissionUtils.hasWriteContactsPermission(context)) {
            LogUtils.printWriteContactsPermissionError(TAG);
            throw new Exception("Write Contacts Permission not granted");
        }

        List<ContactData> contactDataList = getContactsWithPhoneNumber(context, phoneNumber);
        if (contactDataList != null && !contactDataList.isEmpty()) {
            deleteContacts(context, contactDataList, listener);
        } else {
            if (listener != null) {
                listener.onNothingToDelete();
            }
        }
    }

    private void deleteContact(Context context,
                              ContactData contactData,
                              OnContactDeleteListener listener) throws Exception {
        if(!PermissionUtils.hasWriteContactsPermission(context)) {
            LogUtils.printWriteContactsPermissionError(TAG);
            throw new Exception("Write Contacts Permission not granted");
        }

        if (contactData == null) {
            throw new IllegalArgumentException("Contact Data must not be null");
        }

        ContactsDeleter contactsDeleter = ContactsDeleter.Companion.invoke(context);
        contactsDeleter.deleteContact(contactData, contactDataExceptionACResult -> {
            contactDataExceptionACResult.onResult(deletedContactData -> {
                if (listener != null) {
                    listener.onContactDeleted(deletedContactData);
                }
                return Unit.INSTANCE;
            });
            contactDataExceptionACResult.onCompleted(() -> {
                return Unit.INSTANCE;
            });
            contactDataExceptionACResult.doFinally(() -> {
                return Unit.INSTANCE;
            });
            contactDataExceptionACResult.onFailure(error -> {
                if (listener != null) {
                    listener.onContactDeleteFailed(error);
                }
                return Unit.INSTANCE;
            });
            return Unit.INSTANCE;
        });
    }

    private void deleteContacts(Context context,
                              List<ContactData> contactDataList,
                              OnContactsDeleteListener listener) throws Exception {
        if(!PermissionUtils.hasWriteContactsPermission(context)) {
            LogUtils.printWriteContactsPermissionError(TAG);
            throw new Exception("Write Contacts Permission not granted");
        }

        if (contactDataList == null || contactDataList.isEmpty()) {
            throw new IllegalArgumentException("Contact Data List must not be null or empty");
        }

        ContactsDeleter contactsDeleter = ContactsDeleter.Companion.invoke(context);
        contactsDeleter.deleteContacts(contactDataList, contactDataExceptionACResult -> {
            contactDataExceptionACResult.onResult(deletedContactData -> {
                if (listener != null) {
                    listener.onContactsDeleted(deletedContactData);
                }
                return Unit.INSTANCE;
            });
            contactDataExceptionACResult.onCompleted(() -> {
                return Unit.INSTANCE;
            });
            contactDataExceptionACResult.doFinally(() -> {
                return Unit.INSTANCE;
            });
            contactDataExceptionACResult.onFailure(error -> {
                if (listener != null) {
                    listener.onContactsDeleteFailed(error);
                }
                return Unit.INSTANCE;
            });
            return Unit.INSTANCE;
        });
    }

    interface OnContactDeleteListener {
        void onContactDeleted(ContactData deletedContactData);

        void onContactDeleteFailed(Exception exception);

        void onNothingToDelete();
    }

    interface OnContactsDeleteListener {
        void onContactsDeleted(List<ContactData> deletedContactDataList);

        void onContactsDeleteFailed(Map<ContactData, Exception> exceptionList);

        void onNothingToDelete();
    }
}
