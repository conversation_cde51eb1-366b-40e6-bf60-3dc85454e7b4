package com.cubeone.app.modules.vizlog.intercom;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;

import androidx.core.content.ContextCompat;

public class PermissionUtils {

    static boolean hasContactsPermission(Context context) {
        return (hasReadContactsPermission(context) && hasWriteContactsPermission(context));
    }

    static boolean hasReadContactsPermission(Context context) {
        return (ContextCompat.checkSelfPermission(
                context, Manifest.permission.READ_CONTACTS) ==
                PackageManager.PERMISSION_GRANTED);
    }

    static boolean hasWriteContactsPermission(Context context) {
        return (ContextCompat.checkSelfPermission(
                context, Manifest.permission.WRITE_CONTACTS) ==
                PackageManager.PERMISSION_GRANTED);
    }
}
