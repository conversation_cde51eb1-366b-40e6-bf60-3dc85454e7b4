package com.cubeone.app.modules.vizlog.emergency_alert;

import android.app.Service;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.util.Log;

import com.cubeone.app.R;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class EmergencyAlertService extends Service {

    private static final String TAG = "EmergencyAlertService";
    private EmergencyAlertTonePlayer alertTonePlayer;
    private EmergencyAlertReceiver alertReceiver;
    private Handler messageHandler;

    public EmergencyAlertService() {
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        createMessageHandler();
        registerAlertReceiver();
        EmergencyAlertNotificationUtils.createEmergencyAlertNotificationChannel(getApplicationContext());
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Map<String, String> inputParams = extractInputParamsFromIntent(intent);
        if (inputParams != null) {
            String action = inputParams.get("action");
            startForeground(EmergencyAlertNotificationUtils.EMERGENCY_ALERT_SERVICE_NOTIFICATION_ID,
                    EmergencyAlertNotificationUtils.getEmergencyAlertNotification(
                            getApplicationContext(), inputParams));

            startAlertServiceThread(inputParams);
        } else {
            startForeground(EmergencyAlertNotificationUtils.EMERGENCY_ALERT_SERVICE_NOTIFICATION_ID,
                    EmergencyAlertNotificationUtils.getEmergencyAlertNotification(
                            getApplicationContext(), inputParams));
        }
        return START_REDELIVER_INTENT;
    }

    private Map<String, String> extractInputParamsFromIntent(Intent intent) {
        if (intent == null) {
            return null;
        }

        String action = intent.getStringExtra("action");
        String memberName = intent.getStringExtra("member_name");
        String memberMobile = intent.getStringExtra("member_mobile");
        String unitNumber = intent.getStringExtra("unit_number");
        String building = intent.getStringExtra("building");
        String complex = intent.getStringExtra("complex");
        String emergencyType = intent.getStringExtra("emergency_type");

        Map<String, String> params = new HashMap<>();
        params.put("action", action);
        params.put("member_name", memberName);
        params.put("member_mobile", memberMobile);
        params.put("unit_number", unitNumber);
        params.put("building", building);
        params.put("complex", complex);
        params.put("emergency_type", emergencyType);

        return params;
    }

    void startAlertServiceThread(Map<String, String> inputParams) {
        new Thread(() -> handleAlertAction(inputParams)).start();
    }

    synchronized void handleAlertAction(Map<String, String> inputParams) {
        LogUtils.printEmergencyAlertServiceInputParams(TAG, inputParams);

        String action = inputParams.get("action");
        if (action != null && !action.trim().isEmpty()) {
            if (EmergencyAlertConstants.ACTION_EMERGENCY_ALERT.equals(action.trim())) {
                actionRingEmergencyAlert(inputParams);
            }
        }
    }

    void actionRingEmergencyAlert(Map<String, String> inputParams) {
        try {
            ringAlert(inputParams);
        } catch (Exception e) {
            e.printStackTrace();
            alertTonePlayer = null;
            stopEmergencyAlertService();
        }
    }

    void ringAlert(Map<String, String> inputParams) throws Exception {
        if (alertTonePlayer == null || !alertTonePlayer.isPlaying()) {
            alertTonePlayer = new EmergencyAlertTonePlayer();
            Uri alertToneUri = Utils.getRawResourceUri(getApplicationContext(),
                    R.raw.audio_tone_emergency_alert);
            alertTonePlayer.initPlayer(getApplicationContext(), alertToneUri);
            alertTonePlayer.preparePlayerSync(getApplicationContext());
            alertTonePlayer.setLooping(true);
            alertTonePlayer.setLoopCount(Utils.getEmergencyAlertLoopCount());
            alertTonePlayer.playEmergencyAlertTone(player1 -> {
                alertTonePlayer = null;
                fireEndAlertBroadcast();
                stopEmergencyAlertService();
            });
            openEmergencyAlertScreen(inputParams);
        }
    }

    void openEmergencyAlertScreen(Map<String, String> inputParams) {
        Intent intent = new Intent(getApplicationContext(), EmergencyAlertActivity.class);
        if (inputParams != null) {
            intent.putExtra("complex", inputParams.get("complex"));
            intent.putExtra("building", inputParams.get("building"));
            intent.putExtra("unit_number", inputParams.get("unit_number"));
            intent.putExtra("member_name", inputParams.get("member_name"));
            intent.putExtra("member_mobile", inputParams.get("member_mobile"));
            intent.putExtra("emergency_type", inputParams.get("emergency_type"));

            intent.setAction(Intent.ACTION_MAIN);
            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);

            startActivity(intent);
        }
    }

    void stopAlert() {
        Log.d(TAG, "Stopping Alert...");
        if (alertTonePlayer != null) {
            if (alertTonePlayer.isPlaying()) {
                alertTonePlayer.stop();
            }
            alertTonePlayer.release();
            alertTonePlayer = null;
        }
    }

    void stopEmergencyAlertService() {
        Log.d(TAG, "EmergencyAlertService Stopped...");
        stopForeground(true);
        stopSelf();
    }

    void stopEmergencyAlertServiceAfterDelay(int delayInMillis) {
        delayInMillis = Math.max(delayInMillis, 0);
        new Handler().postDelayed(this::stopEmergencyAlertService,
                delayInMillis);
    }

    void fireEndAlertBroadcast() {
        Intent intent = new Intent(EmergencyAlertConstants.ACTION_END_EMERGENCY_ALERT);
        intent.putExtra("source", "service");
        sendBroadcast(intent);
    }

    void registerAlertReceiver() {
        alertReceiver = new EmergencyAlertReceiver(messageHandler);
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(EmergencyAlertConstants.ACTION_END_EMERGENCY_ALERT);
        intentFilter.addAction(EmergencyAlertConstants.ACTION_CANCEL_EMERGENCY_ALERT);
        registerReceiver(alertReceiver, intentFilter);
    }

    void unregisterAlertReceiver() {
        if (alertReceiver != null) {
            unregisterReceiver(alertReceiver);
        }
    }

    void createMessageHandler() {
        messageHandler = new Handler(getMainLooper(), message -> {
            Bundle data = message.getData();
            if (data != null) {
                String action = data.getString("action");
                String source = data.getString("source");

                if (EmergencyAlertConstants.ACTION_CANCEL_EMERGENCY_ALERT.equals(action)
                        && !"service".equals(source)) {
                    Log.d(TAG, "Action = " + action);
                    stopAlert();
                    stopEmergencyAlertService();
                    return true;
                }
            }
            return false;
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        unregisterAlertReceiver();
    }
}
