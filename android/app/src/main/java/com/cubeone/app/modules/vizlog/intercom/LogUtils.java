package com.cubeone.app.modules.vizlog.intercom;

import android.util.Log;

import com.cubeone.app.BuildConfig;
import com.tomash.androidcontacts.contactgetter.entity.ContactData;
import com.tomash.androidcontacts.contactgetter.entity.NameData;
import com.tomash.androidcontacts.contactgetter.entity.PhoneNumber;

import java.util.List;
import java.util.Map;

public class LogUtils {

    static void printReadContactsPermissionError(String tag) {
        Log.e(tag, "Read Contacts Permission not granted");
    }

    static void printWriteContactsPermissionError(String tag) {
        Log.e(tag, "Write Contacts Permission not granted");
    }

    //To be used only for Testing
    static void printContactList(String tag, List<ContactData> contactDataList) {
        if (BuildConfig.DEBUG) {
            if (contactDataList != null && !contactDataList.isEmpty()) {
                int i = 0;
                for (ContactData contactData : contactDataList) {
                    Log.e(tag, "Position = " + i);
                    printContact(tag, contactData);
                    i++;
                }
            }
        }
    }

    //To be used only for Testing
    static void printContact(String tag, ContactData contactData) {
        if (BuildConfig.DEBUG) {
            if (contactData != null) {
                NameData nameData = contactData.getNameData();
                String name = "";
                if (nameData != null) {
                    name = nameData.getFullName();
                }
                List<PhoneNumber> phoneNumberList = contactData.getPhoneList();
                String lookupKey = contactData.getLookupKey();

                Log.e(tag, "Name = " + name);
                Log.e(tag, "Phone Numbers = " + ((phoneNumberList != null && !phoneNumberList.isEmpty()) ?
                        phoneNumberList.toString() : ""));
                Log.e(tag, "Lookup Key = " + lookupKey);
            }
        }
    }

    // To be used only for Testing
    static void printIntercomContactsServiceInputParams(String tag, Map<String, String> inputParams) {
        if (BuildConfig.DEBUG) {
            String action = inputParams.get("action");
            if (action != null && !action.trim().isEmpty()) {
                Log.d(tag, "Action = " + action.trim());
            }

            String name = inputParams.get("name");
            if (name != null && !name.trim().isEmpty()) {
                Log.d(tag, "Name = " + name.trim());
            }

            String phoneNumber = inputParams.get("phone_number");
            if (phoneNumber != null && !phoneNumber.trim().isEmpty()) {
                Log.d(tag, "Phone Number = " + phoneNumber.trim());
            }
        }
    }
}

