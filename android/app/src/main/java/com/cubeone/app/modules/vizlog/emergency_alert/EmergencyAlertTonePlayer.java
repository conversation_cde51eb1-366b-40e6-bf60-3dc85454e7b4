package com.cubeone.app.modules.vizlog.emergency_alert;

import android.app.NotificationManager;
import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Build;
import android.util.Log;

import java.io.IOException;

public class EmergencyAlertTonePlayer extends MediaPlayer {

    private int loopCount = 0;
    private int currentLoopCount = 0;
    private boolean looping = false;
    private static final String TAG = "EmergencyAlertTonePlayer";

    public EmergencyAlertTonePlayer() {
    }

    public int getLoopCount() {
        return loopCount;
    }

    public void setLoopCount(int loopCount) {
        this.loopCount = loopCount;
    }

    @Override
    public void setLooping(boolean b) {
        super.setLooping(false);
        this.looping = b;
    }

    @Override
    public boolean isLooping() {
        return looping;
    }

    public void initPlayer(Context context, Uri alertToneUri) throws Exception {
        setDataSource(context, alertToneUri);
        initPlayerBeforePreparation(context);
    }

    public void initPlayerBeforePreparation(Context context) {
        Log.d(TAG, "Initializing player before preparing");
        setAudioStreamType(AudioManager.STREAM_ALARM);
        setAudioAttributes(new AudioAttributes.Builder()
                .setFlags(AudioAttributes.FLAG_AUDIBILITY_ENFORCED)
                .setUsage(AudioAttributes.USAGE_ALARM)
                .build());
    }

    public void initPlayerAfterPreparation(Context context) {
        Log.d(TAG, "Initializing player after preparing");
        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

        setVolume(1.0f, 1.0f);

        if (audioManager != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &&
                    (notificationManager.isNotificationPolicyAccessGranted())) {
                audioManager.setRingerMode(AudioManager.RINGER_MODE_NORMAL);
                audioManager.setStreamVolume(AudioManager.STREAM_ALARM,
                        audioManager.getStreamMaxVolume(AudioManager.STREAM_ALARM), 0);
            }
            audioManager.setMode(AudioManager.MODE_NORMAL);
            audioManager.setSpeakerphoneOn(true);

            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
                audioManager.requestAudioFocus(i -> {},
                        AudioManager.STREAM_ALARM,
                        AudioManager.AUDIOFOCUS_GAIN);
            } else {
                audioManager.requestAudioFocus(new AudioFocusRequest.Builder(
                        AudioManager.AUDIOFOCUS_GAIN)
                        .build());
            }
        }
    }

    public void preparePlayerAsync(final Context context, OnPreparePlayerListener prepareListener) {
        setOnPreparedListener(mediaPlayer -> {
            initPlayerAfterPreparation(context);
            if (prepareListener != null) {
                prepareListener.onPlayerPrepared(mediaPlayer);
            }
        });
        Log.d(TAG, "Preparing player async");
        prepareAsync();
    }

    public void preparePlayerSync(Context context) throws Exception {
        Log.d(TAG, "Preparing player sync");
        prepare();
        initPlayerAfterPreparation(context);
    }

    public void playEmergencyAlertTone(OnPlaybackFinishListener finishListener) {
        setOnCompletionListener(mediaPlayer -> {
            if (isLooping()) {
                if (currentLoopCount < getLoopCount()) {
                    start();
                } else {
                    stop();
                    release();
                    currentLoopCount = 0;
                    if (finishListener != null) {
                        finishListener.onPlaybackFinished(mediaPlayer);
                    }
                }
                currentLoopCount++;
            } else {
                stop();
                release();
                loopCount = 0;
                currentLoopCount = 0;
                if (finishListener != null) {
                    finishListener.onPlaybackFinished(mediaPlayer);
                }
            }
        });
        start();
        currentLoopCount++;
    }

    public interface OnPreparePlayerListener {
        void onPlayerPrepared(MediaPlayer player);
    }

    public interface OnPlaybackFinishListener {
        void onPlaybackFinished(MediaPlayer player);
    }
}
