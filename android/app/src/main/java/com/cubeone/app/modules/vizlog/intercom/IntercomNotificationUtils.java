package com.cubeone.app.modules.vizlog.intercom;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.os.Build;

import androidx.core.app.NotificationCompat;

public class IntercomNotificationUtils {

    public static final String INTERCOM_CHANNEL_ID = "com.cubeone.app.INTERCOM";
    public static final String INTERCOM_CHANNEL_NAME = "Intercom Notifications";
    public static final int INTERCOM_CONTACT_SERVICE_NOTIFICATION_ID = 7778;

    public static void createIntercomNotificationChannel(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

            NotificationChannel mChannel = notificationManager.getNotificationChannel(INTERCOM_CHANNEL_ID);
            if (mChannel == null) {
                mChannel = new NotificationChannel(
                        INTERCOM_CHANNEL_ID,
                        INTERCOM_CHANNEL_NAME,
                        NotificationManager.IMPORTANCE_HIGH);
                mChannel.enableVibration(true);
                mChannel.setVibrationPattern(new long[]{100, 200, 300, 400, 500, 400, 300, 200, 400});

                notificationManager.createNotificationChannel(mChannel);
            }
        }
    }

    public static Notification getIntercomContactUpdateNotification(Context context) {
        int ic_popup_reminder = android.R.drawable.ic_popup_reminder;
        String title = "Updating Members...";
        String message = "Adding members to your contact list";

        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, INTERCOM_CHANNEL_ID);
        builder.setContentTitle(title)
                .setSmallIcon(ic_popup_reminder)
                .setContentText(message)
                .setDefaults(NotificationCompat.DEFAULT_ALL)
                .setPriority(NotificationCompat.PRIORITY_MAX)
                .setAutoCancel(true).setWhen(0)
                .setVibrate(new long[]{100, 200, 300, 400, 500, 400, 300, 200, 400});

        return builder.build();
    }

    public static Notification getIntercomContactRemoveNotification(Context context) {
        int ic_popup_reminder = android.R.drawable.ic_popup_reminder;
        String title = "Removing Members...";
        String message = "Removing members from your contact list";

        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, INTERCOM_CHANNEL_ID);
        builder.setContentTitle(title)
                .setSmallIcon(ic_popup_reminder)
                .setContentText(message)
                .setDefaults(NotificationCompat.DEFAULT_ALL)
                .setPriority(NotificationCompat.PRIORITY_MAX)
                .setAutoCancel(true).setWhen(0)
                .setVibrate(new long[]{100, 200, 300, 400, 500, 400, 300, 200, 400});

        return builder.build();
    }

}
