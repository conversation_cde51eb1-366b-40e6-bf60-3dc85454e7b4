package com.cubeone.app.modules.vizlog.emergency_alert;

import android.app.KeyguardManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.os.Build;
import android.os.Bundle;

import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.snackbar.Snackbar;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import android.os.Handler;
import android.os.IBinder;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.cubeone.app.R;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

public class EmergencyAlertActivity extends AppCompatActivity {

    private static final String TAG = "EmergencyAlertActivity";
    private ImageView emergencyAlertImg;
    private TextView emergencyAlertTitleTxt, emergencyAlertDescriptionTxt;
    private TextView bldgTxt, bldgFlatTxt, societyTxt,
            memberNameTxt, memberMobileTxt, profileInitialsTxt;
    private Button cancelAlertBtn;
    private EmergencyAlertReceiver alertReceiver;
    private Handler messageHandler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "Activity created");

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setShowWhenLocked(true);
            setTurnScreenOn(true);
            Window window = getWindow();
            if (window != null) {
                window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            }
            KeyguardManager keyguardManager = (KeyguardManager) getSystemService(KEYGUARD_SERVICE);
            if (keyguardManager != null) {
                keyguardManager.requestDismissKeyguard(this,
                        new KeyguardManager.KeyguardDismissCallback() {
                    @Override
                    public void onDismissError() {
                        super.onDismissError();
                    }

                    @Override
                    public void onDismissSucceeded() {
                        super.onDismissSucceeded();
                    }

                    @Override
                    public void onDismissCancelled() {
                        super.onDismissCancelled();
                    }
                });
            }
        } else {
            Window window = getWindow();
            if (window != null) {
                window.addFlags(WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD);
                window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED);
                window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                window.addFlags(WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
            }
        }

        setContentView(R.layout.activity_emergency_alert);

        initUI();
        bindData(extractDataFromIntent(getIntent()));

        createMessageHandler();
        registerAlertReceiver();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        Log.d(TAG, "Activity onNewIntent");
    }

    private Map<String, String> extractDataFromIntent(Intent intent) {
        String society = "", building = "", flat = "",
                memberName = "", memberMobile = "", emergencyType = null;
        if (intent != null) {
            society = intent.getStringExtra("complex");
            building = intent.getStringExtra("building");
            flat = intent.getStringExtra("unit_number");
            memberName = intent.getStringExtra("member_name");
            memberMobile = intent.getStringExtra("member_mobile");
            emergencyType = intent.getStringExtra("emergency_type");
        }
        Map<String, String> data = new HashMap<>();
        data.put("complex", society);
        data.put("building", building);
        data.put("unit_number", flat);
        data.put("member_name", memberName);
        data.put("member_mobile", memberMobile);
        data.put("emergency_type", emergencyType);

        return data;
    }

    private void initUI() {
        emergencyAlertImg = findViewById(R.id.img_emergency_alert);
        emergencyAlertTitleTxt = findViewById(R.id.txt_title_emergency_alert);
        emergencyAlertDescriptionTxt = findViewById(R.id.txt_description_emergency_alert);

        bldgTxt = findViewById(R.id.txt_bldg);
        bldgFlatTxt = findViewById(R.id.txt_bldg_flat);
        societyTxt = findViewById(R.id.txt_society);
        memberNameTxt = findViewById(R.id.txt_member_name);
        memberMobileTxt = findViewById(R.id.txt_member_mobile);
        profileInitialsTxt = findViewById(R.id.txt_profile_initials);

        cancelAlertBtn = findViewById(R.id.btn_cancel_alert);
        cancelAlertBtn.setOnClickListener(view -> actionCancelAlert());
    }

    private void bindData(Map<String, String> data) {
        String emergencyAlertType = data.get("emergency_type");
        EmergencyAlertCategory alertCategory = Utils.getEmergencyAlertCategoryOrDefault(
                getApplicationContext(), emergencyAlertType);
        if (alertCategory == null) {
            emergencyAlertTitleTxt.setText(getString(R.string.title_emergency_alert_default));
            emergencyAlertDescriptionTxt.setText(getString(R.string.msg_emergency_alert_default));
            emergencyAlertImg.setImageResource(R.drawable.emergency);
        } else {
            emergencyAlertTitleTxt.setText(alertCategory.getTitle());
            emergencyAlertDescriptionTxt.setText(alertCategory.getDescription());
            emergencyAlertImg.setImageResource(alertCategory.getIcon());
        }

        String society = data.get("complex");
        societyTxt.setText(society);

        String building = data.get("building");
        bldgTxt.setText(building);
        bldgTxt.setVisibility((building != null && !building.trim().isEmpty())
                ? View.VISIBLE : View.GONE);

        String flat = data.get("unit_number");
        bldgFlatTxt.setText((flat));

        String memberName = data.get("member_name");
        memberNameTxt.setText(memberName);

        String memberMobile = data.get("member_mobile");
        memberMobileTxt.setText(memberMobile);

        String profileInitials = com.cubeone.app.utils.Utils.getNameInitials(memberName);
        profileInitialsTxt.setText(profileInitials);
        profileInitialsTxt.setVisibility((profileInitials != null && !profileInitials.trim().isEmpty())
                ? View.VISIBLE : View.GONE);

    }

    private void actionCancelAlert() {
        fireCancelAlertBroadcast();
        finishAndRemoveTask();
    }

    void registerAlertReceiver() {
        alertReceiver = new EmergencyAlertReceiver(messageHandler);
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(EmergencyAlertConstants.ACTION_END_EMERGENCY_ALERT);
        intentFilter.addAction(EmergencyAlertConstants.ACTION_CANCEL_EMERGENCY_ALERT);
        registerReceiver(alertReceiver, intentFilter);
    }

    void unregisterAlertReceiver() {
        if (alertReceiver != null) {
            unregisterReceiver(alertReceiver);
        }
    }

    void fireCancelAlertBroadcast() {
        Intent intent = new Intent(EmergencyAlertConstants.ACTION_CANCEL_EMERGENCY_ALERT);
        intent.putExtra("source", "activity");
        sendBroadcast(intent);
    }

    void createMessageHandler() {
        messageHandler = new Handler(getMainLooper(), message -> {
            Bundle data = message.getData();
            if (data != null) {
                String action = data.getString("action");
                String source = data.getString("source");

                if ((EmergencyAlertConstants.ACTION_CANCEL_EMERGENCY_ALERT.equals(action)
                        || EmergencyAlertConstants.ACTION_END_EMERGENCY_ALERT.equals(action))
                        && !"activity".equals(source)) {
                    Log.d(TAG, "Action = " + action);
                    Log.d(TAG, "Finishing Activity");
                    finishAndRemoveTask();
                    return true;
                }
            }
            return false;
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "Activity Destroyed");
        unregisterAlertReceiver();
    }
}