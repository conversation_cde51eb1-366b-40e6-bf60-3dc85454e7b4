package com.cubeone.app.modules.vizlog.emergency_alert;

import android.util.Log;

import com.cubeone.app.BuildConfig;

import java.util.List;
import java.util.Map;

public class LogUtils {

    // To be used only for Testing
    static void printEmergencyAlertServiceInputParams(String tag, Map<String, String> inputParams) {
        if (BuildConfig.DEBUG) {
            String action = inputParams.get("action");
            if (action != null && !action.trim().isEmpty()) {
                Log.d(tag, "Action = " + action.trim());
            }

            String memberName = inputParams.get("member_name");
            if (memberName != null && !memberName.trim().isEmpty()) {
                Log.d(tag, "Member Name = " + memberName.trim());
            }

            String memberMobile = inputParams.get("member_mobile");
            if (memberMobile != null && !memberMobile.trim().isEmpty()) {
                Log.d(tag, "Member Mobile = " + memberMobile.trim());
            }

            String unitNumber = inputParams.get("unit_number");
            if (unitNumber != null && !unitNumber.trim().isEmpty()) {
                Log.d(tag, "Unit Number = " + unitNumber.trim());
            }

            String building = inputParams.get("building");
            if (building != null && !building.trim().isEmpty()) {
                Log.d(tag, "Building = " + building.trim());
            }

            String complex = inputParams.get("complex");
            if (complex != null && !complex.trim().isEmpty()) {
                Log.d(tag, "Complex = " + complex.trim());
            }
        }
    }
}

