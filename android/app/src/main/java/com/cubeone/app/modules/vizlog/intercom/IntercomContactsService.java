package com.cubeone.app.modules.vizlog.intercom;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.util.Log;

import com.tomash.androidcontacts.contactgetter.entity.ContactData;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class IntercomContactsService extends Service {

    private static final String TAG = "IntercomContactsService";

    public IntercomContactsService() {
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        IntercomNotificationUtils.createIntercomNotificationChannel(getApplicationContext());
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Map<String, String> inputParams = extractInputParamsFromIntent(intent);
        if (inputParams != null) {
            String action = inputParams.get("action");
            startForeground(IntercomNotificationUtils.INTERCOM_CONTACT_SERVICE_NOTIFICATION_ID,
                    (IntercomConstants.ACTION_INTERCOM_CONTACT_REMOVE.equals(action) ?
                            IntercomNotificationUtils.getIntercomContactRemoveNotification(getApplicationContext()) :
                            IntercomNotificationUtils.getIntercomContactUpdateNotification(getApplicationContext())));

            startContactServiceThread(inputParams);
        } else {
            startForeground(IntercomNotificationUtils.INTERCOM_CONTACT_SERVICE_NOTIFICATION_ID,
                    IntercomNotificationUtils.getIntercomContactUpdateNotification(getApplicationContext()));
        }
        return START_REDELIVER_INTENT;
    }

    Map<String, String> extractInputParamsFromIntent(Intent intent) {
        if (intent == null) {
            return null;
        }

        String action = intent.getStringExtra("action");
        String name = intent.getStringExtra("name");
        String phoneNumber = intent.getStringExtra("phone_number");

        Map<String, String> params = new HashMap<>();
        params.put("action", action);
        params.put("name", name);
        params.put("phone_number", phoneNumber);

        return params;
    }

    void startContactServiceThread(Map<String, String> inputParams) {
        new Thread(() -> handleContactAction(inputParams)).start();
    }

    synchronized void handleContactAction(Map<String, String> inputParams) {
        LogUtils.printIntercomContactsServiceInputParams(TAG, inputParams);

        String action = inputParams.get("action");
        if (action != null && !action.trim().isEmpty()) {
            if (IntercomConstants.ACTION_INTERCOM_CONTACT_UPDATE.equals(action.trim())) {
                actionUpdateIntercomContact(inputParams);
            } else if (IntercomConstants.ACTION_INTERCOM_CONTACT_REMOVE.equals(action.trim())) {
                actionRemoveIntercomContact(inputParams);
            }
        }
    }

    void actionUpdateIntercomContact(Map<String, String> inputParams) {
        Log.d(TAG, "IntercomContactsService Started...");
        Log.d(TAG, "Adding Member Contact...");

        String name = inputParams.get("name");
        String phoneNumber = inputParams.get("phone_number");

        if ((name == null || name.trim().isEmpty()) ||
                (phoneNumber == null || phoneNumber.trim().isEmpty())) {
            stopIntercomContactsService();
            return;
        }

        name = name.trim();
        phoneNumber = phoneNumber.trim();

        try {
            IntercomContactsManager contactsManager = IntercomContactsManager.getInstance();
            List<ContactData> contactList = contactsManager.getContactsWithPhoneNumber(getApplicationContext(), phoneNumber);
            if (contactList == null || contactList.isEmpty()) {
                Log.d(TAG, "Adding Contact...");
                contactsManager.addNewContact(getApplicationContext(), name, phoneNumber);
                Log.d(TAG, "Contact Added...");
                stopIntercomContactsService();
            } else {
                Log.d(TAG, "Deleting Contact...");
                String finalPhoneNumber = phoneNumber;
                String finalName = name;
                contactsManager.deleteContactsWithPhoneNumber(
                        getApplicationContext(),
                        phoneNumber,
                        new IntercomContactsManager.OnContactsDeleteListener() {
                            @Override
                            public void onContactsDeleted(List<ContactData> deletedContactDataList) {
                                try {
                                    Log.d(TAG, "Contact Deleted...");
                                    Log.d(TAG, "Adding Contact...");
                                    contactsManager.addNewContact(getApplicationContext(), finalName, finalPhoneNumber);
                                    Log.d(TAG, "Contact Added...");
                                } catch (Exception e) {
                                    e.printStackTrace();
                                } finally {
                                    stopIntercomContactsService();
                                }
                            }

                            @Override
                            public void onContactsDeleteFailed(Map<ContactData, Exception> exceptionList) {
                                Log.e(TAG, "Failed to delete contacts...");
                                stopIntercomContactsService();
                            }

                            @Override
                            public void onNothingToDelete() {
                                try {
                                    Log.d(TAG, "No contacts found to delete, Skipping...");
                                    Log.d(TAG, "Adding Contact...");
                                    contactsManager.addNewContact(getApplicationContext(), finalName, finalPhoneNumber);
                                    Log.d(TAG, "Contact Added...");
                                } catch (Exception e) {
                                    e.printStackTrace();
                                } finally {
                                    stopIntercomContactsService();
                                }
                            }
                        });
            }
        } catch (Exception e) {
            e.printStackTrace();
            stopIntercomContactsService();
        }
    }

    void actionRemoveIntercomContact(Map<String, String> inputParams) {
        Log.d(TAG, "IntercomContactsService Started...");
        Log.d(TAG, "Deleting Member Contact...");

        String phoneNumber = inputParams.get("phone_number");

        if ((phoneNumber == null || phoneNumber.trim().isEmpty())) {
            stopIntercomContactsService();
            return;
        }

        phoneNumber = phoneNumber.trim();

        try {
            IntercomContactsManager contactsManager = IntercomContactsManager.getInstance();
            List<ContactData> contactList = contactsManager.getContactsWithPhoneNumber(getApplicationContext(), phoneNumber);
            if (contactList != null && !contactList.isEmpty()) {
                Log.d(TAG, "Deleting Contact...");
                contactsManager.deleteContactsWithPhoneNumber(
                        getApplicationContext(),
                        phoneNumber,
                        new IntercomContactsManager.OnContactsDeleteListener() {
                            @Override
                            public void onContactsDeleted(List<ContactData> deletedContactDataList) {
                                Log.d(TAG, "Contact Deleted...");
                                stopIntercomContactsService();
                            }

                            @Override
                            public void onContactsDeleteFailed(Map<ContactData, Exception> exceptionList) {
                                Log.e(TAG, "Failed to delete contacts...");
                                stopIntercomContactsService();
                            }

                            @Override
                            public void onNothingToDelete() {
                                Log.d(TAG, "No contacts found to delete, Skipping...");
                                stopIntercomContactsService();
                            }
                        });
            } else {
                Log.d(TAG, "No contacts found to delete, Skipping...");
                stopIntercomContactsService();
            }
        } catch (Exception e) {
            e.printStackTrace();
            stopIntercomContactsService();
        }
    }

    void stopIntercomContactsService() {
        Log.d(TAG, "IntercomContactsService Stopped...");
        stopForeground(true);
        stopSelf();
    }
}
