package com.cubeone.app.modules.vizlog.emergency_alert;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

import androidx.core.app.NotificationCompat;

import com.cubeone.app.R;

import java.util.Map;

public class EmergencyAlertNotificationUtils {

    public static final String EMERGENCY_ALERT_CHANNEL_ID = "com.cubeone.app.EMERGENCY_ALERT";
    public static final String EMERGENCY_ALERT_CHANNEL_NAME = "Emergency Alert Notifications";
    public static final int EMERGENCY_ALERT_SERVICE_NOTIFICATION_ID = 3500;
    private static final int REQ_CODE_CANCEL_EMERGENCY_ALERT = 7001;
    private static final int REQ_CODE_OPEN_EMERGENCY_ALERT = 7002;

    public static void createEmergencyAlertNotificationChannel(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

            NotificationChannel mChannel = notificationManager.getNotificationChannel(EMERGENCY_ALERT_CHANNEL_ID);
            if (mChannel == null) {
                mChannel = new NotificationChannel(
                        EMERGENCY_ALERT_CHANNEL_ID,
                        EMERGENCY_ALERT_CHANNEL_NAME,
                        NotificationManager.IMPORTANCE_HIGH);
                mChannel.enableVibration(true);
                mChannel.setVibrationPattern(new long[]{100, 200, 300, 400, 500, 400, 300, 200, 400});

                notificationManager.createNotificationChannel(mChannel);
            }
        }
    }

    public static Notification getEmergencyAlertNotification(Context context, Map<String, String> data) {
        int ic_popup_reminder = android.R.drawable.ic_popup_reminder;
        String title = "Emergency";
        String message = getEmergencyAlertNotificationMessage(context, data);

        Intent cancelAlertIntent = new Intent(EmergencyAlertConstants.ACTION_CANCEL_EMERGENCY_ALERT);
        cancelAlertIntent.putExtra("source", "notification");
        PendingIntent cancelAlertPendingIntent = PendingIntent.getBroadcast(context,
                REQ_CODE_CANCEL_EMERGENCY_ALERT,
                cancelAlertIntent,
                PendingIntent.FLAG_UPDATE_CURRENT);

        Intent openAlertIntent = new Intent(context, EmergencyAlertActivity.class);
        if (data != null) {
            openAlertIntent.putExtra("complex", data.get("complex"));
            openAlertIntent.putExtra("building", data.get("building"));
            openAlertIntent.putExtra("unit_number", data.get("unit_number"));
            openAlertIntent.putExtra("member_name", data.get("member_name"));
            openAlertIntent.putExtra("member_mobile", data.get("member_mobile"));
        }
        PendingIntent openAlertPendingIntent = PendingIntent.getActivity(context,
                REQ_CODE_OPEN_EMERGENCY_ALERT,
                openAlertIntent,
                PendingIntent.FLAG_UPDATE_CURRENT);

        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, EMERGENCY_ALERT_CHANNEL_ID);
        builder.setContentTitle(title)
                .setSmallIcon(ic_popup_reminder)
                .setContentText(message)
                .setDefaults(NotificationCompat.DEFAULT_ALL)
                .setPriority(NotificationCompat.PRIORITY_MAX)
                .setAutoCancel(true).setWhen(0)
                .setVibrate(new long[]{100, 200, 300, 400, 500, 400, 300, 200, 400})
                .setContentIntent(openAlertPendingIntent)
                .addAction(android.R.id.closeButton,
                        context.getResources().getString(R.string.btn_cancel_alert),
                        cancelAlertPendingIntent);

        return builder.build();
    }

    private static String getEmergencyAlertNotificationMessage(Context context, Map<String, String> data) {
        if (data != null && !data.isEmpty()) {
            return context.getResources().getString(R.string.msg_emergency_alert,
                    data.get("member_name"),
                    data.get("unit_number"),
                    data.get("building"),
                    data.get("complex"));
        } else {
            return context.getResources().getString(R.string.msg_default_emergency_alert);
        }
    }

}
