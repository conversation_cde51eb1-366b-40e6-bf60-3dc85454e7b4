package com.cubeone.app.modules.vizlog.emergency_alert;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;

public class EmergencyAlertReceiver extends BroadcastReceiver {

    Handler messageHandler;

    public EmergencyAlertReceiver() {}

    public EmergencyAlertReceiver(Handler messageHandler) {
        this.messageHandler = messageHandler;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        if ( intent != null) {
            if (messageHandler != null) {
                Message message = new Message();
                Bundle data = new Bundle();
                data.putString("action", intent.getAction());
                data.putString("source", intent.getStringExtra("source"));
                message.setData(data);

                messageHandler.sendMessage(message);
            }
        }
    }
}
