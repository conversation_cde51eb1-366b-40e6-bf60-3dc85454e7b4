package com.cubeone.app.modules.vizlog.emergency_alert;

import android.content.ContentResolver;
import android.content.Context;
import android.net.Uri;

import com.cubeone.app.BuildConfig;
import com.cubeone.app.R;

import java.io.File;

public class Utils {

    public static Uri getRawResourceUri(Context context, int resourceId) {
        return new Uri.Builder()
                .scheme(ContentResolver.SCHEME_ANDROID_RESOURCE)
                .authority(context.getPackageName())
                .path(context.getResources().getResourceTypeName(resourceId) +
                        File.separator +
                        context.getResources().getResourceEntryName(resourceId))
                .build();
    }

    public static int getEmergencyAlertLoopCount(boolean forceDebug) {
        return (forceDebug || BuildConfig.DEBUG)
                ? EmergencyAlertConstants.EMERGENCY_ALERT_LOOP_COUNT_DEBUG
                : EmergencyAlertConstants.EMERGENCY_ALERT_LOOP_COUNT_RELEASE;
    }

    public static int getEmergencyAlertLoopCount() {
        return getEmergencyAlertLoopCount(false);
    }

    public static EmergencyAlertCategory getEmergencyAlertCategoryOrDefault(Context context, String emergencyAlertType) {
        EmergencyAlertCategory alertCategory;
        if (EmergencyAlertType.GAS.value.equals(emergencyAlertType)) {
            alertCategory = new EmergencyAlertCategory();
            alertCategory.setType(EmergencyAlertType.GAS);
            alertCategory.setTitle(context.getString(R.string.title_emergency_alert_fire));
            alertCategory.setDescription(context.getString(R.string.msg_emergency_alert_fire));
            alertCategory.setIcon(R.drawable.firetruck);
        } else if (EmergencyAlertType.FIRE.value.equals(emergencyAlertType)) {
            alertCategory = new EmergencyAlertCategory();
            alertCategory.setType(EmergencyAlertType.FIRE);
            alertCategory.setTitle(context.getString(R.string.title_emergency_alert_fire));
            alertCategory.setDescription(context.getString(R.string.msg_emergency_alert_fire));
            alertCategory.setIcon(R.drawable.firetruck);
        } else if (EmergencyAlertType.MEDICAL.value.equals(emergencyAlertType)) {
            alertCategory = new EmergencyAlertCategory();
            alertCategory.setType(EmergencyAlertType.MEDICAL);
            alertCategory.setTitle(context.getString(R.string.title_emergency_alert_medical));
            alertCategory.setDescription(context.getString(R.string.msg_emergency_alert_medical));
            alertCategory.setIcon(R.drawable.ambulance);
        } else if (EmergencyAlertType.ELEVATOR.value.equals(emergencyAlertType)) {
            alertCategory = new EmergencyAlertCategory();
            alertCategory.setType(EmergencyAlertType.ELEVATOR);
            alertCategory.setTitle(context.getString(R.string.title_emergency_alert_elevator));
            alertCategory.setDescription(context.getString(R.string.msg_emergency_alert_elevator));
            alertCategory.setIcon(R.drawable.elevator);
        } else if (EmergencyAlertType.THEFT.value.equals(emergencyAlertType)) {
            alertCategory = new EmergencyAlertCategory();
            alertCategory.setType(EmergencyAlertType.THEFT);
            alertCategory.setTitle(context.getString(R.string.title_emergency_alert_theft));
            alertCategory.setDescription(context.getString(R.string.msg_emergency_alert_theft));
            alertCategory.setIcon(R.drawable.police);
        } else if (EmergencyAlertType.OTHER.value.equals(emergencyAlertType)) {
            alertCategory = new EmergencyAlertCategory();
            alertCategory.setType(EmergencyAlertType.OTHER);
            alertCategory.setTitle(context.getString(R.string.title_emergency_alert_default));
            alertCategory.setDescription(context.getString(R.string.msg_emergency_alert_default));
            alertCategory.setIcon(R.drawable.emergency);
        } else {
            alertCategory = new EmergencyAlertCategory();
            alertCategory.setType(EmergencyAlertType.OTHER);
            alertCategory.setTitle(context.getString(R.string.title_emergency_alert_default));
            alertCategory.setDescription(context.getString(R.string.msg_emergency_alert_default));
            alertCategory.setIcon(R.drawable.emergency);
        }

        return alertCategory;
    }
}
