// ignore_for_file: must_be_immutable

import 'dart:collection';
import 'dart:developer';

import 'package:common_config/utils/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/services/text_formatter.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:sso_futurescape/config/environment/environment.dart';
import 'package:sso_futurescape/config/strings/strings.dart';
import 'package:sso_futurescape/custom_widgets/custom_elevated_btn.dart';
import 'package:sso_futurescape/custom_widgets/custom_form_widget.dart';
import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/presentor/module/mobikwik_payment/mobikwik_presenter.dart';
import 'package:sso_futurescape/presentor/module/mobikwik_payment/mobikwik_view.dart';
import 'package:sso_futurescape/ui/base/loading_constants.dart';
import 'package:sso_futurescape/ui/module/chsone/payment/rent_payment_success.dart';
import 'package:sso_futurescape/utils/app_constant.dart';
import 'package:sso_futurescape/utils/app_utils.dart';
import 'package:sso_futurescape/utils/storage/sso_storage.dart';
import 'package:sso_futurescape/utils/ui/loading_error_utils.dart';
import 'package:sso_futurescape/utils/widget_utils.dart';

import '../../../../../config/colors/color.dart';

class OnepayRentpayStep2 extends StatefulWidget {
  Map? accountDetails;

  OnepayRentpayStep2({this.accountDetails});

  @override
  _OnepayRentpayStep2State createState() => _OnepayRentpayStep2State();
}

class _OnepayRentpayStep2State extends State<OnepayRentpayStep2>
    implements MobikwikView {
  late MobikwikPresenter mobikwikPresenter;
  late Razorpay _razorpay;

  bool _loadError = false;
  LoadingErrorType? _loadErrorType;
  String? _loadErrorMsg;
  String? rent_amount;
  String? pan_number;
  String? note;
  bool isLoading = false;

  TextEditingController _noteCRTL = TextEditingController();
  TextEditingController _panCRTL = TextEditingController();
  FocusNode _rentAmountFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _loadRentPaymentPage();
  }

  @override
  void dispose() {
    super.dispose();
    _razorpay.clear();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(new FocusNode());
      },
      child: MyScrollView(
        isScrollable: true,
        pageTitle: 'Pay Rent',
        pageBody: _loadError
            ? _buildErrorWidget()
            : Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: _buildPage()),
        floatingActionButton: _loadError
            ? SizedBox()
            : Container(
                margin: EdgeInsets.fromLTRB(20, 0, 20, 0),
                child: totalPayableRentAmt != null &&
                        totalPayableRentAmt.toString().isNotEmpty
                    ? CustomLargeBtn(
                        onPressed: () {
                          log("pay button clicked");
                          if (widget.accountDetails != null &&
                              widget.accountDetails!['landlord_account_id'] !=
                                  null) {
                            if (pan_number != null && pan_number!.isNotEmpty) {
                              if (pan_number!.length == 10) {
                                // Proceed with the existing logic
                                _loadRentPaymentPage(tag: 'proceed');
                              } else {
                                // Handle the case where pan_number is not 10 characters long
                                Toastly.error(
                                    context, 'Please enter valid pan number.');
                              }
                            } else {
                              // Handle the case where pan_number is not present
                              _loadRentPaymentPage(tag: 'proceed');
                            }
                          } else {
                            Toastly.error(context,
                                'Landlord account not found. Please try again or contact support.');
                          }
                        },
                        text: "Pay ₹${totalPayableRentAmt}",
                      )
                    : SizedBox(),
              ),
      ),

      // Stack(
      //   children: [
      //     MyScrollView(
      //       pageTitle: 'Pay Rent',
      //       floatingActionButtonLocation:
      //           FloatingActionButtonLocation.centerDocked,
      //       floatingActionButton: _loadError
      //           ? Container(height: 0)
      //           : Container(
      //               width: double.infinity,
      //               padding: EdgeInsets.symmetric(
      //                 horizontal: 15,
      //               ),
      //               decoration: BoxDecoration(
      //                 color: Theme.of(context).colorScheme.surface,
      //                 borderRadius: BorderRadius.only(
      //                   topLeft: Radius.circular(4),
      //                   topRight: Radius.circular(4),
      //                 ),
      //                 boxShadow: [
      //                   BoxShadow(
      //                     color: Colors.grey.withOpacity(0.5),
      //                     spreadRadius: 1,
      //                     blurRadius: 3,
      //                     offset: Offset(0, -1),
      //                   ),
      //                 ],
      //               ),
      //               child: Row(
      //                 children: [
      //                   Container(
      //                     padding: EdgeInsets.fromLTRB(10, 0, 25, 0),
      //                     child: Text(
      //                         totalPayableRentAmt != null
      //                             ? '₹ ' +
      //                                 (totalPayableRentAmt.toStringAsFixed(2))
      //                                     .toString()
      //                             : '₹ 00.0',
      //                         style: TextStyle(
      //                           color: Theme.of(context).colorScheme.onSurface,
      //                           fontStyle: Theme.of(context)
      //                               .textTheme
      //                               .titleLarge
      //                               ?.fontStyle,
      //                         )),
      //                   ),
      //                   Expanded(
      //                     child: ElevatedButton(
      //                       onPressed: () async {
      //                         // bool userConsent = await PermissionHelper.instance
      //                         //     .requestPaymentsPermissionForOnepay(context);
      //                         // if (!userConsent) {
      //                         //   return;
      //                         // }
      //                         // _loadRentPaymentPage(tag: 'proceed');
      //
      //                         if (widget.accountDetails != null &&
      //                             widget.accountDetails![
      //                                     'landlord_account_id'] !=
      //                                 null) {
      //                           if (pan_number != null &&
      //                               pan_number!.isNotEmpty) {
      //                             if (pan_number!.length == 10) {
      //                               // Proceed with the existing logic
      //                               _loadRentPaymentPage(tag: 'proceed');
      //                             } else {
      //                               // Handle the case where pan_number is not 10 characters long
      //                               Toastly.error(context,
      //                                   'Please enter valid pan number.');
      //                             }
      //                           } else {
      //                             // Handle the case where pan_number is not present
      //                             _loadRentPaymentPage(tag: 'proceed');
      //                           }
      //                         } else {
      //                           Toastly.error(context,
      //                               'Landlord account not found. Please try again or contact support.');
      //                         }
      //                       },
      //                       style: ElevatedButton.styleFrom(
      //                           backgroundColor:
      //                               Theme.of(context).colorScheme.onSurface,
      //                           // backgroundColor: Theme.of(context).colorScheme.secondary,
      //                           textStyle: Theme.of(context)
      //                               .textTheme
      //                               .labelLarge
      //                               ?.copyWith(
      //                                 color:
      //                                     Theme.of(context).colorScheme.surface,
      //                               )),
      //                       child: Text(
      //                         "Proceed to Pay",
      //                       ),
      //                     ),
      //                   ),
      //                 ],
      //               )),
      //       pageBody: _loadError
      //           ? _buildErrorWidget()
      //           : SingleChildScrollView(
      //               child:
      //                   //  Form(
      //                   //     key: _formKey,
      //                   //     child:
      //                   Column(
      //               mainAxisAlignment: MainAxisAlignment.start,
      //               crossAxisAlignment: CrossAxisAlignment.start,
      //               children: [
      //                 // Container(
      //                 //   padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      //                 //   child: Text('Popular Services'.toLowerCase(),
      //                 //     style: TextStyle(color: FsColor.basicprimary, fontFamily: 'Gilroy-SemiBold', fontSize: FSTextStyle.h5size, letterSpacing: 1),
      //                 //   ),
      //                 // ),
      //
      //                 Row(
      //                   children: [
      //                     Expanded(
      //                       child: Column(
      //                         mainAxisAlignment: MainAxisAlignment.start,
      //                         crossAxisAlignment: CrossAxisAlignment.start,
      //                         children: [
      //                           Container(
      //                             child: Text(
      //                               widget.accountDetails!['account_details']
      //                                   ['business_name'],
      //                               style: Theme.of(context)
      //                                   .textTheme
      //                                   .titleMedium!
      //                                   .merge(TextStyle(
      //                                       color: Theme.of(context)
      //                                           .colorScheme
      //                                           .onSurface
      //                                           .withOpacity(0.7))),
      //                             ),
      //                           ),
      //                           SizedBox(height: 5),
      //                           Container(
      //                             child: Text(
      //                               bankAccountNumberFormatter(),
      //                               style:
      //                                   Theme.of(context).textTheme.bodyMedium,
      //                             ),
      //                           ),
      //                         ],
      //                       ),
      //                     ),
      //                     // Container(
      //                     //   child: IconButton(
      //                     //     icon: Icon(
      //                     //       Icons.edit,
      //                     //       color: Theme.of(context).primaryColor,
      //                     //       size: 20,
      //                     //     ),
      //                     //     onPressed: () {},
      //                     //   ),
      //                     // ),
      //                   ],
      //                 ),
      //
      //                 SizedBox(height: 10),
      //
      //                 CustomTextField(
      //                   hintText: 'Rent Amount',
      //                   keyboardType: TextInputType.number,
      //                   inputFormatters: [
      //                     FilteringTextInputFormatter.digitsOnly
      //                   ],
      //                   onChanged: (value) {
      //                     if (value == "") {
      //                       rent_amount = '0';
      //                     } else {
      //                       rent_amount = value;
      //                       if (int.parse(rent_amount!) >= 50000) {
      //                         showPanNumberField = true;
      //                       } else {
      //                         showPanNumberField = false;
      //                       }
      //                     }
      //                     _loadRentPaymentPage(tag: 'calculate');
      //                   },
      //                   title: 'Rent Amount',
      //                 ),
      //                 CustomTextField(
      //                   hintText: !showPanNumberField
      //                       ? "Owner's PAN Number (Optional)"
      //                       : "Owner's PAN Number",
      //                   textCapitalization: TextCapitalization.characters,
      //                   title: "Owner's PAN Number",
      //                   onChanged: (value) {
      //                     pan_number = value;
      //                   },
      //                   length: 10,
      //                   // validator must of 10 only
      //                 ),
      //                 CustomTextField(
      //                   hintText: "Note",
      //                   title: "Note",
      //                   lines: 5,
      //                   onChanged: (String? value) {
      //                     note = value;
      //                   },
      //                 ),
      //
      //                 Container(
      //                   padding: EdgeInsets.symmetric(vertical: 10),
      //                   child: Text(
      //                     'Price Details',
      //                     style:
      //                         Theme.of(context).textTheme.titleMedium?.copyWith(
      //                               color: Theme.of(context)
      //                                   .colorScheme
      //                                   .onSurface
      //                                   .withOpacity(0.5),
      //                             ),
      //                   ),
      //                 ),
      //                 Row(
      //                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
      //                   crossAxisAlignment: CrossAxisAlignment.center,
      //                   children: [
      //                     Text('Total Amount',
      //                         style: Theme.of(context)
      //                             .textTheme
      //                             .titleMedium
      //                             ?.copyWith(
      //                               color:
      //                                   Theme.of(context).colorScheme.onSurface,
      //                             )),
      //                     Text(
      //                       rent_amount != null && totalPayableRentAmt != null
      //                           ? '₹ ' +
      //                               int.parse(rent_amount!)
      //                                   .toDouble()
      //                                   .toString()
      //                           : '₹ 00.0',
      //                       // '₹ 49,999.99',
      //                       style: Theme.of(context)
      //                           .textTheme
      //                           .titleMedium
      //                           ?.copyWith(
      //                             color:
      //                                 Theme.of(context).colorScheme.onSurface,
      //                           ),
      //                     ),
      //                   ],
      //                 ),
      //                 SizedBox(
      //                   height: 5,
      //                 ),
      //                 Row(
      //                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
      //                   crossAxisAlignment: CrossAxisAlignment.center,
      //                   children: [
      //                     Text('Processing Fees',
      //                         style: Theme.of(context)
      //                             .textTheme
      //                             .titleMedium
      //                             ?.copyWith(
      //                               color:
      //                                   Theme.of(context).colorScheme.onSurface,
      //                             )),
      //                     Text(
      //                       rent_amount != null && totalPayableRentAmt != null
      //                           ? '₹ ' +
      //                               (totalPayableRentAmt -
      //                                       int.parse(rent_amount!))
      //                                   .toStringAsFixed(2)
      //                                   .toString()
      //                           : '₹ 00.0',
      //                       style: Theme.of(context)
      //                           .textTheme
      //                           .titleMedium
      //                           ?.copyWith(
      //                             color:
      //                                 Theme.of(context).colorScheme.onSurface,
      //                           ),
      //                     ),
      //                   ],
      //                 ),
      //                 SizedBox(
      //                   height: 5,
      //                 ),
      //                 Row(
      //                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
      //                   crossAxisAlignment: CrossAxisAlignment.center,
      //                   children: [
      //                     Text('Amount Payable',
      //                         style: TextStyle(
      //                           color: Theme.of(context).colorScheme.onSurface,
      //                           fontStyle: Theme.of(context)
      //                               .textTheme
      //                               .titleMedium
      //                               ?.fontStyle,
      //                         )),
      //                     Text(
      //                       rent_amount != null && totalPayableRentAmt != null
      //                           ? '₹ ' +
      //                               (totalPayableRentAmt..toStringAsFixed(2))
      //                                   .toString()
      //                           : '₹ 00.0',
      //                       style: Theme.of(context)
      //                           .textTheme
      //                           .titleMedium
      //                           ?.copyWith(
      //                             color:
      //                                 Theme.of(context).colorScheme.onSurface,
      //                           ),
      //                     ),
      //                   ],
      //                 ),
      //                 Padding(
      //                   padding: EdgeInsets.only(
      //                     top: 10,
      //                     bottom: 100,
      //                   ),
      //                   child: Text(
      //                       'It may take up to 48 working hours for the amount to be reflected in the bank account.',
      //                       style: Theme.of(context)
      //                           .textTheme
      //                           .bodySmall!
      //                           .merge(TextStyle(
      //                             height: 1.25,
      //                             letterSpacing: 1.2,
      //                           ))),
      //                 ),
      //               ],
      //             )
      //               // ),
      //               ),
      //     ),
      //     isLoading
      //         ? Container(
      //             color: Theme.of(context).colorScheme.surface.withOpacity(0.7),
      //             alignment: Alignment.center,
      //             child: CircularProgressIndicator(
      //                 valueColor: AlwaysStoppedAnimation<Color>(
      //                     Theme.of(context).primaryColor)),
      //           )
      //         : Container(),
      //   ],
      // ),
    );
  }

  String? email, mobile;
  var paymentId;

  void _handlePaymentSuccess(PaymentSuccessResponse response) {
    isLoading = true;
    paymentId = response.paymentId;
    print(response.paymentId.toString());
    completeRentPaymentStatus(status: paymentDetail());
    setState(() {});
  }

  bankAccountNumberFormatter() {
    var val =
        widget.accountDetails!['bank_account']['account_number'].toString();
    return val.replaceAll(val.substring(0, val.length - 4), "******");
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    // Navigator.pop(context, context);
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    Fluttertoast.showToast(msg: "EXTERNAL_WALLET: " + response.walletName!);
  }

  Map details() {
    return {'rent_amt': rent_amount, 'pan_no': pan_number, 'note': note};
  }

  bool showPanNumberField = false;

  formValidation({required Map data}) {
    if (data['rent_amt'] == null || data['rent_amt'] == "") {
      return Toastly.error(context, 'Please enter amount.');
    } else if (int.parse(data['rent_amt']) == 0 ||
        int.parse(data['rent_amt']) > 100000) {
      return Toastly.error(
          context, 'Amount should be greater than 1 and less than 1,00,000.');
    } else if (data['pan_no'] == null && showPanNumberField) {
      return Toastly.error(context, 'Please enter pan no.');
    } else if (showPanNumberField &&
        !RegExp('[A-Z]{3}[ABCFGHLJPTF]{1}[A-Z]{1}[0-9]{4}[A-Z]{1}')
            .hasMatch(data['pan_no'])) {
      return Toastly.error(context, 'Please enter valid pan no.');
    }
    // if (data['note'] == null) {
    //   return Toastly.error(context, 'Please enter note.');
    // }
    else {
      dataBeforeRentPayment();
    }
  }

  getDetail() {
    SsoStorage.getUserProfile().then((profile) {
      setState(() {
        email = profile['email'];
        mobile = profile['mobile'];
        // name = this.operatorDetail['Operator Name'];
        // desc = billersData['cellNumber'];
      });
    });
    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
    // functionCall();
  }

  Widget _buildErrorWidget() {
    return Center(
      child: WidgetUtils.getErrorWidget(
          module: AppConstant.ONE_PAY,
          errorMsg: _loadErrorMsg!,
          errorType: _loadErrorType,
          showErrorIcon: true,
          shouldRetry: LoadingErrorUtils.canRetry(_loadErrorType),
          onRetryPressed: () {
            _loadRentPaymentPage();
          },
          retryButtonColor: Theme.of(context).primaryColor),
    );
  }

  Widget _buildPage() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CircleAvatar(
          radius: 30,
          backgroundColor: Theme.of(context).primaryColor,
          child: Icon(
            Icons.account_balance_wallet,
            color: Theme.of(context).colorScheme.onPrimary,
            size: 30,
          ),
        ),
        Text(
          widget.accountDetails!['account_details']['business_name'],
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        Text(
          bankAccountNumberFormatter(),
          // 'xxxxxxxx2545 - HDFCXXXX',
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
        ),
        TextFormField(
          focusNode: _rentAmountFocusNode,
          onChanged: (value) {
            if (value == "") {
              rent_amount = '0';
            } else {
              rent_amount = value;
              if (int.parse(rent_amount!) >= 50000) {
                showPanNumberField = true;
              } else {
                showPanNumberField = false;
              }
            }
            _loadRentPaymentPage(tag: 'calculate');
          },
          maxLength: 7,
          style: Theme.of(context).textTheme.headlineLarge!.copyWith(
                fontSize: 52,
              ),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
          ],
          decoration: InputDecoration(
            counterText: '',
            hintText: '0',
            hintStyle: Theme.of(context).textTheme.headlineLarge!.copyWith(
                  fontSize: 52,
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                ),
            prefixIcon: Text(
              '₹',
              style: Theme.of(context).textTheme.headlineLarge!.copyWith(
                    fontSize: 52,
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.7),
                  ),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide.none,
            ),
          ),
        ),
        Container(
          margin: EdgeInsets.only(
            bottom: 10,
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.03),
            borderRadius: BorderRadius.circular(15),
          ),
          child: Column(
            children: [
              ListTile(
                // contentPadding: EdgeInsets.,
                leading: Icon(
                  Symbols.description,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                title: Text(
                  'Add a note',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),

                subtitle: _noteCRTL.text.isNotEmpty
                    ? Text(
                        _noteCRTL.text,
                        style: Theme.of(context).textTheme.labelMedium,
                      )
                    : null,
                trailing: Icon(
                  Icons.arrow_forward_ios,
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                  size: 18,
                ),
                onTap: () {
                  _rentAmountFocusNode.unfocus();
                  showBottomFields(
                    'note',
                  );
                },
              ),
              Divider(
                indent: 15,
                endIndent: 15,
                thickness: 1,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.1),
              ),
              ListTile(
                onTap: () {
                  _rentAmountFocusNode.unfocus();
                  showBottomFields(
                    'pan',
                  );
                },
                // contentPadding: EdgeInsets.,
                leading: Icon(
                  Symbols.fact_check,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                title: Text(
                  'Add society PAN number',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                subtitle: Text(
                  _panCRTL.text.isEmpty
                      ? showPanNumberField
                          ? 'Required for amount above ₹50k'
                          : 'Optional'
                      : _panCRTL.text,
                  style: Theme.of(context).textTheme.labelMedium,
                ),

                trailing: Icon(
                  Icons.arrow_forward_ios,
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                  size: 18,
                ),
              ),
              Divider(
                indent: 15,
                endIndent: 15,
                thickness: 1,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.1),
              ),
              ListTile(
                onTap: () {
                  _rentAmountFocusNode.unfocus();
                  showBottomFields(
                    'total',
                  );
                },
                leading: Icon(
                  Symbols.receipt_long,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                title: (totalPayableRentAmt != null)
                    ? Text(
                        'Total amount ₹${(totalPayableRentAmt.toString())}',
                        style: Theme.of(context).textTheme.bodyMedium,
                      )
                    : Text(
                        'Total amount ₹0',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                subtitle: Text(
                  'Includes processing fees',
                  style: Theme.of(context).textTheme.labelMedium,
                ),
                trailing: Icon(
                  Icons.arrow_forward_ios,
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                  size: 18,
                ),
              ),
            ],
          ),
        ),
        Container(
          // color: Colors.red,
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).size.height * 0.2,
          ),
          child: Text(
            "It may take up to 48 working hours for the amount to be reflected in the bank account.",
            style: Theme.of(context).textTheme.labelMedium,
          ),
        )
      ],
    );
  }

  void showBottomFields(String title) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            margin: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom + 20,
            ),
            child: switch (title) {
              'note' => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Add a note',
                      style: Theme.of(context).textTheme.headlineLarge,
                    ),
                    CustomTextField(
                      textController: _noteCRTL,
                      title: 'Enter your note',
                      onFieldSubmitted: (value) {
                        FocusScope.of(context).unfocus();
                      },
                      hintText: 'Enter your note',
                    ),
                    CustomLargeBtn(
                      onPressed: () {
                        setState(() {
                          _noteCRTL.text = _noteCRTL.text;
                        });
                        Navigator.pop(context);
                      },
                      text: 'Save',
                    ),
                  ],
                ),
              'pan' => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Add society PAN number',
                      style: Theme.of(context).textTheme.headlineLarge,
                    ),
                    CustomTextField(
                      title: 'Enter society PAN number',
                      textController: _panCRTL,
                      onFieldSubmitted: (value) {
                        FocusScope.of(context).unfocus();
                      },
                      textCapitalization: TextCapitalization.characters,
                      length: 10,
                      autovalidate: true,
                      validator: (value) {
                        if (value!.isEmpty) {
                          return null;
                        } else if (value.length != 10) {
                          return 'PAN number must be 10 characters long';
                        }
                        return null;
                      },
                      hintText: 'Enter society PAN number',
                    ),
                    CustomLargeBtn(
                      onPressed: () {
                        if (_panCRTL.text.length == 10 ||
                            _panCRTL.text.isEmpty) {
                          setState(() {
                            pan_number = _panCRTL.text;
                          });
                          Navigator.pop(context);
                        } else {
                          Toastly.error(context, 'Please enter valid pan no.');
                        }
                      },
                      text: 'Save',
                    ),
                  ],
                ),
              'total' => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      title: Text(
                        'Amount Details',
                        style: Theme.of(context).textTheme.headlineLarge,
                      ),
                      trailing: IconButton(
                          style: IconButton.styleFrom(
                            backgroundColor: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.03),
                          ),
                          // color: Colors.red,
                          icon: Icon(
                            Symbols.close,
                            color: Colors.red,
                          ),
                          onPressed: () {
                            Navigator.pop(context);
                          }),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            'Total Amount',
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                          Text(
                            rent_amount != null && totalPayableRentAmt != null
                                ? '₹ ' +
                                    int.parse(rent_amount!)
                                        .toDouble()
                                        .toString()
                                : '₹ 00.0',
                            // '₹ 49,999.99',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  color:
                                      Theme.of(context).colorScheme.onSurface,
                                ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            'Processing Fees',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          Text(
                            rent_amount != null && totalPayableRentAmt != null
                                ? '₹ ' +
                                    (totalPayableRentAmt -
                                            int.parse(rent_amount!))
                                        .toStringAsFixed(2)
                                : '₹ 00.0',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 10),
                      decoration: BoxDecoration(
                          border: Border(
                              top: BorderSide(
                                  width: 1, color: FsColor.lightgrey))),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            'Amount Payable',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface),
                          ),
                          Text(
                            rent_amount != null && totalPayableRentAmt != null
                                ? '₹ ' + (totalPayableRentAmt).toString()
                                : '₹ 00.0',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              _ => Container(),
            },
          ),
        );
      },
    );
  }

  void _loadRentPaymentPage({tag}) {
    AppUtils.checkInternetConnection().then((value) {
      if (value) {
        _loadError = false;
        _loadErrorType = null;
        _loadErrorMsg = null;
        mobikwikPresenter = new MobikwikPresenter(this);
        if (tag == 'proceed') {
          formValidation(data: details());
        } else if (tag == 'calculate') {
          calulateTotalRentAmount(rentAmount: rent_amount);
        } else {
          getDetail();
          rent_amount = null;
          totalPayableRentAmt = null;
          setState(() {});
        }
      } else {
        _loadError = true;
        _loadErrorMsg = FsString.ERROR_NO_INTERNET_RETRY;
        _loadErrorType = LoadingErrorType.INTERNET;
      }
      setState(() {});
    });
  }

  dataToSave({rechargeAmt}) {
    setState(() {
      // rechargeAmount = rechargeAmt;
    });
    HashMap<String, String> h = new HashMap();
    // h["type"] = widget.selectedOption;

    return h;
  }

  var totalPayableRentAmt;

  dataBeforeRentPayment() {
    setState(() {
      isLoading = true;
    });
    print(widget.accountDetails.toString());
    mobikwikPresenter
        .initiateRentPaymnet(callingType: "inititiateRentPayment", data: {
      'account_name': widget.accountDetails!['account_details']
          ['business_name'],
      'total_payable_amt': totalPayableRentAmt.toStringAsFixed(2).toString(),
      'actual_rent_amt': rent_amount.toString(),
      'account_id': widget.accountDetails!['landlord_account_id'],
      'pan': pan_number,
      'note': note
    });
  }

  paymentDetail() {
    return {
      'order_id': orderId,
      'payment_id': paymentId,
      'totalPayableAmount': totalPayableRentAmt.toStringAsFixed(2),
      'account_id': widget.accountDetails!['landlord_account_id']
    };
  }

  completeRentPaymentStatus({status}) {
    mobikwikPresenter.updatePaymentResponse(
        data: paymentDetail(), callingType: 'paymentStatus');
  }

  onClickPayButton() {
    dataBeforeRentPayment();
  }

  calulateTotalRentAmount({rentAmount}) {
    mobikwikPresenter.calculatePayableAmount(
        callingType: "calulateTotalRentAmount", amount: rentAmount);
  }

  payBill({order_id}) {
    var currentConfig = Environment().getCurrentConfig();
    var options = {
      'key': currentConfig.razorpayApiKey,
      'order_id': order_id,
      'amount': totalPayableRentAmt.toStringAsFixed(2),
      'name': widget.accountDetails!['account_details']['business_name'],
      'description': 'rent payment',
      'options': {
        'checkout': {
          'method': {'netbanking': '1', 'card': '1', 'upi': '1', 'wallet': '1'},
        },
      },
      'prefill': {
        'contact': mobile ?? '',
        'email': email ?? '',
      }
    };
    // dataBeforeBillPayment();
    print("options::" + options.toString());
    try {
      _razorpay.open(options);
    } catch (e) {
      print(e);
    }
    setState(() {});
  }

  @override
  error(error, {callingType}) {
    if (callingType == "inititiateRentPayment") {
      setState(() {
        isLoading = false;
      });
      return Toastly.error(context, error);
    } else if (callingType == "calulateTotalRentAmount") {
      return Toastly.error(context, error);
    } else if (callingType == 'paymentStatus') {
      return Toastly.error(context, error);
    }
  }

  @override
  failure(failed, {callingType}) {
    if (callingType == "inititiateRentPayment") {
      setState(() {
        isLoading = false;
      });
      return Toastly.error(context, failed);
    } else if (callingType == "calulateTotalRentAmount") {
      return Toastly.error(context, failed);
    } else if (callingType == 'paymentStatus') {
      return Toastly.error(context, failed);
    }
  }

  @override
  ordererror(error, {callingType}) {
    throw UnimplementedError();
  }

  @override
  orderfailure(failed, {callingType}) {
    throw UnimplementedError();
  }

  @override
  ordersuccess(success, {callingType, String? searchedText}) {
    if (success != null) {
      setState(() {
        // billersData['order_id'] = success['data']['order_id'];
      });
    }
    payBill();
  }

  var orderId;

  paymentStatusPage(paymentStatus) {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => RentPaymentSuccussful(
                  isFromHistory: false,
                  comingFrom: FsString.RENT_PAYMENT,
                  paymentStatus: paymentStatus,
                  landLordAcDetails: widget.accountDetails,
                )));
  }

  @override
  success(success, {callingType, String? searchedText}) {
    if (callingType == "inititiateRentPayment") {
      if (success['data'] != null) {
        orderId = success['data']['order_id'];
        isLoading = false;
        Toastly.success(context, success['data']['message']);
        payBill(order_id: success['data']['order_id']);
      }
    } else if (callingType == "calulateTotalRentAmount") {
      if (success['data'] != null) {
        totalPayableRentAmt = success['data']['amount'];
      }
      setState(() {});
    } else if (callingType == 'paymentStatus') {
      if (success['data'] != null) {
        paymentStatusPage(success['data']);
        print('payment status::' + success['data'].toString());
      }
    }
  }
}
