// ignore_for_file: must_be_immutable

import 'dart:collection';
import 'dart:developer';

import 'package:common_config/utils/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/services/text_formatter.dart';

import 'package:material_symbols_icons/material_symbols_icons.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:sso_futurescape/config/colors/color.dart';
import 'package:sso_futurescape/config/environment/environment.dart';
import 'package:sso_futurescape/config/strings/strings.dart';
import 'package:sso_futurescape/custom_widgets/custom_elevated_btn.dart';
import 'package:sso_futurescape/custom_widgets/custom_form_widget.dart';
import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/presentor/module/mobikwik_payment/mobikwik_presenter.dart';
import 'package:sso_futurescape/presentor/module/mobikwik_payment/mobikwik_view.dart';
import 'package:sso_futurescape/ui/base/loading_constants.dart';
import 'package:sso_futurescape/ui/module/chsone/payment/rent_payment_success.dart';
import 'package:sso_futurescape/utils/app_constant.dart';
import 'package:sso_futurescape/utils/auth/token_validation_service.dart';
import 'package:sso_futurescape/utils/app_utils.dart';
import 'package:sso_futurescape/utils/storage/sso_storage.dart';
import 'package:sso_futurescape/utils/ui/loading_error_utils.dart';
import 'package:sso_futurescape/utils/widget_utils.dart';

class OnepayMaintenancepayStep2 extends StatefulWidget {
  Map? accountDetails;

  OnepayMaintenancepayStep2({this.accountDetails});

  @override
  _OnepayMaintenancepayStep2State createState() =>
      _OnepayMaintenancepayStep2State();
}

class _OnepayMaintenancepayStep2State extends State<OnepayMaintenancepayStep2>
    implements MobikwikView {
  late MobikwikPresenter mobikwikPresenter;
  late Razorpay _razorpay;

  bool _loadError = false;
  LoadingErrorType? _loadErrorType;
  String? _loadErrorMsg;
  String? maintenance_amount;
  String? pan_number;
  String? note;
  bool isLoading = false;
  TextEditingController _noteCRTL = TextEditingController();
  TextEditingController _panCRTL = TextEditingController();
  FocusNode _maintenanceFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _loadMaintenancePayPage();
  }

  @override
  void dispose() {
    super.dispose();
    _noteCRTL.dispose();
    _panCRTL.dispose();
    _maintenanceFocusNode.dispose();
    _razorpay.clear();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: MyScrollView(
        pageTitle: 'Maintenance Payment',
        bottomNavigationBar: _loadError
            ? SizedBox()
            : Container(
                margin: EdgeInsets.fromLTRB(
                  20,
                  0,
                  20,
                  20,
                ),
                child: totalPayableAmt != null &&
                        totalPayableAmt.toString().isNotEmpty
                    ? CustomLargeBtn(
                        onPressed: () {
                          log("pay button clicked");
                          _loadMaintenancePayPage(tag: 'proceed');
                        },
                        text: "Pay ₹${totalPayableAmt}",
                      )
                    : SizedBox(),
              ),
        pageBody: _loadError
            ? _buildErrorWidget()
            : Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Theme.of(context).primaryColor,
                    child: Icon(
                      Icons.account_balance_wallet,
                      color: Theme.of(context).colorScheme.onPrimary,
                      size: 30,
                    ),
                  ),
                  Text(
                    widget.accountDetails!['account_details']['business_name'],
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                  Text(
                    bankAccountNumberFormatter(),
                    // 'xxxxxxxx2545 - HDFCXXXX',
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.7),
                        ),
                  ),
                  TextFormField(
                    focusNode: _maintenanceFocusNode,
                    onChanged: (value) {
                      if (value == "") {
                        setState(() {
                          totalPayableAmt = 0;
                        });
                        maintenance_amount = '0';
                      } else {
                        maintenance_amount = value;
                        if (int.parse(maintenance_amount!) >= 50000) {
                          showPanNumberField = true;
                        } else {
                          showPanNumberField = false;
                        }
                      }
                      _loadMaintenancePayPage(tag: 'calculate');
                    },
                    maxLength: 7,
                    style: Theme.of(context).textTheme.headlineLarge!.copyWith(
                          fontSize: 52,
                        ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    decoration: InputDecoration(
                      counterText: '',
                      hintText: '0',
                      hintStyle:
                          Theme.of(context).textTheme.headlineLarge!.copyWith(
                                fontSize: 52,
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withOpacity(0.3),
                              ),
                      prefixIcon: Text(
                        '₹',
                        style:
                            Theme.of(context).textTheme.headlineLarge!.copyWith(
                                  fontSize: 52,
                                  color: Theme.of(context)
                                      .colorScheme
                                      .onSurface
                                      .withOpacity(0.7),
                                ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide.none,
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                      bottom: 10,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.03),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Column(
                      children: [
                        ListTile(
                          // contentPadding: EdgeInsets.,
                          leading: Icon(
                            Symbols.description,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                          title: Text(
                            'Add a note',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          subtitle: _noteCRTL.text.isNotEmpty
                              ? Text(
                                  _noteCRTL.text,
                                  style:
                                      Theme.of(context).textTheme.labelMedium,
                                )
                              : null,
                          trailing: Icon(
                            Icons.arrow_forward_ios,
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.5),
                            size: 18,
                          ),
                          onTap: () {
                            _maintenanceFocusNode.unfocus();
                            showBottomFields(
                              'note',
                            );
                          },
                        ),
                        Divider(
                          indent: 15,
                          endIndent: 15,
                          thickness: 1,
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.1),
                        ),
                        ListTile(
                          onTap: () {
                            _maintenanceFocusNode.unfocus();
                            showBottomFields(
                              'pan',
                            );
                          },
                          // contentPadding: EdgeInsets.,
                          leading: Icon(
                            Symbols.fact_check,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                          title: Text(
                            'Add society PAN number',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          subtitle: Text(
                            _panCRTL.text.isEmpty
                                ? showPanNumberField
                                    ? 'Required for amount above ₹50k'
                                    : 'Optional'
                                : _panCRTL.text,
                            style: Theme.of(context).textTheme.labelMedium,
                          ),

                          trailing: Icon(
                            Icons.arrow_forward_ios,
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.5),
                            size: 18,
                          ),
                        ),
                        Divider(
                          indent: 15,
                          endIndent: 15,
                          thickness: 1,
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.1),
                        ),
                        ListTile(
                          onTap: () {
                            _maintenanceFocusNode.unfocus();
                            showBottomFields(
                              'total',
                            );
                          },
                          leading: Icon(
                            Symbols.receipt_long,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                          title: (totalPayableAmt != null)
                              ? Text(
                                  'Total amount ₹${(totalPayableAmt.toString())}',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                )
                              : Text(
                                  'Total amount ₹0',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                          subtitle: Text(
                            'Includes processing fees',
                            style: Theme.of(context).textTheme.labelMedium,
                          ),
                          trailing: Icon(
                            Icons.arrow_forward_ios,
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.5),
                            size: 18,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    "It may take up to 48 working hours for the amount to be reflected in the bank account.",
                    style: Theme.of(context).textTheme.labelMedium,
                  ),
                ],
              ),
      ),
    );
  }

  void showBottomFields(String title) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            margin: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom + 20,
            ),
            child: switch (title) {
              'note' => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Add a note',
                      style: Theme.of(context).textTheme.headlineLarge,
                    ),
                    CustomTextField(
                      textController: _noteCRTL,
                      title: 'Enter your note',
                      onFieldSubmitted: (value) {
                        FocusScope.of(context).unfocus();
                      },
                      hintText: 'Enter your note',
                    ),
                    CustomLargeBtn(
                      onPressed: () {
                        setState(() {
                          _noteCRTL.text = _noteCRTL.text;
                        });
                        Navigator.pop(context);
                      },
                      text: 'Save',
                    ),
                  ],
                ),
              'pan' => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Add society PAN number',
                      style: Theme.of(context).textTheme.headlineLarge,
                    ),
                    CustomTextField(
                      title: 'Enter society PAN number',
                      textController: _panCRTL,
                      onFieldSubmitted: (value) {
                        FocusScope.of(context).unfocus();
                      },
                      textCapitalization: TextCapitalization.characters,
                      length: 10,
                      autovalidate: true,
                      validator: (value) {
                        if (value?.length != 10) {
                          return 'Please enter a valid PAN number';
                        }
                        return null;
                      },
                      hintText: 'Enter society PAN number',
                    ),
                    CustomLargeBtn(
                      onPressed: () {
                        if (_panCRTL.text.length == 10) {
                          setState(() {
                            pan_number = _panCRTL.text;
                          });
                          Navigator.pop(context);
                        } else {
                          Toastly.error(context, 'Please enter valid pan no.');
                        }
                      },
                      text: 'Save',
                    ),
                  ],
                ),
              'total' => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Amount Details',
                      style: Theme.of(context).textTheme.headlineLarge,
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            'Total Amount',
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                          Text(
                            maintenance_amount != null &&
                                    totalPayableAmt != null
                                ? '₹ ' +
                                    int.parse(maintenance_amount!)
                                        .toDouble()
                                        .toString()
                                : '₹ 00.0',
                            // '₹ 49,999.99',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  color:
                                      Theme.of(context).colorScheme.onSurface,
                                ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            'Processing Fees',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          Text(
                            maintenance_amount != null &&
                                    totalPayableAmt != null
                                ? '₹ ' +
                                    (totalPayableAmt -
                                            int.parse(maintenance_amount!))
                                        .toStringAsFixed(2)
                                : '₹ 00.0',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 10),
                      decoration: BoxDecoration(
                          border: Border(
                              top: BorderSide(
                                  width: 1, color: FsColor.lightgrey))),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            'Amount Payable',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface),
                          ),
                          Text(
                            maintenance_amount != null &&
                                    totalPayableAmt != null
                                ? '₹ ' + (totalPayableAmt).toString()
                                : '₹ 00.0',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              _ => Container(),
            },
          ),
        );
      },
    );
  }

  String? email, mobile;
  var paymentId;

  void _handlePaymentSuccess(PaymentSuccessResponse response) {
    isLoading = true;
    paymentId = response.paymentId;
    print(response.paymentId.toString());
    completeMaintenancePaymentStatus(status: paymentDetail());
    setState(() {});
  }

  bankAccountNumberFormatter() {
    var val =
        widget.accountDetails!['bank_account']['account_number'].toString();
    return val.replaceAll(val.substring(0, val.length - 4), "******");
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    setState(() {
      isLoading = false;
    });
    print("Payment Error: ${response.code} - ${response.message}");

    // Check if error is authentication-related
    if (response.code == 401 || response.code == 403) {
      _showAuthenticationError("Authentication expired. Please login again.");
    } else {
      Toastly.error(
          context, "Payment failed: ${response.message ?? 'Unknown error'}");
    }
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    setState(() {
      isLoading = false;
    });
    print("External Wallet: ${response.walletName}");
    Toastly.success(
        context, "Redirecting to ${response.walletName ?? 'external wallet'}");
  }

  Map details() {
    return {
      'amount': maintenance_amount,
      'pan_no': _panCRTL.text,
      'note': _noteCRTL.text,
    };
  }

  bool showPanNumberField = false;

  formValidation({required Map data}) {
    if (data['amount'] == null || data['amount'] == "") {
      return Toastly.error(context, 'Please enter amount.');
    } else if (int.parse(data['amount']) == 0 ||
        int.parse(data['amount']) > 100000) {
      return Toastly.error(
          context, 'Amount should be greater than 1 and less than 1,00,000');
    } else if (data['pan_no'] == null && showPanNumberField) {
      return Toastly.error(context, 'Please enter pan no.');
    } else if (showPanNumberField &&
        !RegExp('[A-Z]{3}[ABCFGHLJPTF]{1}[A-Z]{1}[0-9]{4}[A-Z]{1}')
            .hasMatch(data['pan_no'])) {
      return Toastly.error(context, 'Please enter valid pan no.');
    }
    if (data['note'] == null) {
      return Toastly.error(context, 'Please enter note.');
    } else {
      dataBeforeMaintenancePayment();
    }
  }

  getDetail() {
    SsoStorage.getUserProfile().then((profile) {
      setState(() {
        email = profile['email'];
        mobile = profile['mobile'];
        // name = this.operatorDetail['Operator Name'];
        // desc = billersData['cellNumber'];
      });
    });
    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
    // functionCall();
  }

  Widget _buildErrorWidget() {
    return Center(
      child: WidgetUtils.getErrorWidget(
          module: AppConstant.ONE_PAY,
          errorMsg: _loadErrorMsg!,
          errorType: _loadErrorType,
          showErrorIcon: true,
          shouldRetry: LoadingErrorUtils.canRetry(_loadErrorType),
          onRetryPressed: () {
            _loadMaintenancePayPage();
          },
          retryButtonColor: Theme.of(context).primaryColor),
    );
  }

  void _loadMaintenancePayPage({tag}) {
    AppUtils.checkInternetConnection().then((value) {
      if (value) {
        _loadError = false;
        _loadErrorType = null;
        _loadErrorMsg = null;
        mobikwikPresenter = new MobikwikPresenter(this);
        if (tag == 'proceed') {
          formValidation(data: details());
        } else if (tag == 'calculate') {
          calulateTotalMaintenanceAmount(maintenanceAmount: maintenance_amount);
        } else {
          getDetail();
          maintenance_amount = null;
          totalPayableAmt = null;
          setState(() {});
        }
      } else {
        _loadError = true;
        _loadErrorMsg = FsString.ERROR_NO_INTERNET_RETRY;
        _loadErrorType = LoadingErrorType.INTERNET;
      }
      setState(() {});
    });
  }

  dataToSave({rechargeAmt}) {
    setState(() {
      // rechargeAmount = rechargeAmt;
    });
    HashMap<String, String> h = new HashMap();
    // h["type"] = widget.selectedOption;

    return h;
  }

  var totalPayableAmt;

  dataBeforeMaintenancePayment() {
    setState(() {
      isLoading = true;
    });
    print(widget.accountDetails.toString());
    var paymentData = {
      'account_name': widget.accountDetails!['account_details']
          ['business_name'],
      'total_payable_amt': totalPayableAmt.toString(),
      'actual_amt': maintenance_amount.toString(),
      'account_id': widget.accountDetails!['account_id'],
      'pan': _panCRTL.text,
      'note': _noteCRTL.text,
    };
    log("dataBeforeMaintenancePayment ${paymentData.toString()}");
    mobikwikPresenter.initiateSocietyPayment(
      callingType: "inititiateSocietyPayment",
      data: paymentData,
    );
  }

  paymentDetail() {
    return {
      'order_id': orderId,
      'payment_id': paymentId,
      'totalPayableAmount': totalPayableAmt,
      'account_id': widget.accountDetails!['account_id'],
    };
  }

  completeMaintenancePaymentStatus({status}) {
    mobikwikPresenter.completeSocietyPayment(
        data: paymentDetail(), callingType: 'paymentStatus');
  }

  onClickPayButton() {
    dataBeforeMaintenancePayment();
  }

  calulateTotalMaintenanceAmount({maintenanceAmount}) {
    mobikwikPresenter.calPayableMaintenanceAmt(
        callingType: "calulateTotalMaintenanceAmount",
        amount: maintenanceAmount);
  }

  payBill({order_id}) async {
    try {
      // Validate authentication state before payment
      final validationResult =
          await TokenValidationService.validateAuthenticationState();
      if (!validationResult.isValid) {
        _showAuthenticationError(validationResult.message);
        return;
      }

      var currentConfig = Environment().getCurrentConfig();
      var options = {
        'key': currentConfig.razorpayApiKey,
        'order_id': order_id,
        'amount': totalPayableAmt,
        'name': widget.accountDetails!['account_details']['business_name'],
        'description': 'Society Maintenance',
        'options': {
          'checkout': {
            'method': {
              'netbanking': '1',
              'card': '1',
              'upi': '1',
              'wallet': '1'
            },
          },
        },
        'prefill': {
          'contact': mobile ?? '',
          'email': email ?? '',
        }
      };
      // dataBeforeBillPayment();
      print("options::" + options.toString());

      _razorpay.open(options);
      setState(() {});
    } catch (e) {
      print('Payment initiation error: $e');
      _showAuthenticationError("Payment failed to start: ${e.toString()}");
    }
  }

  @override
  error(error, {callingType}) {
    if (callingType == "inititiateSocietyPayment") {
      setState(() {
        isLoading = false;
      });
      return Toastly.error(context, error);
    } else if (callingType == "calulateTotalMaintenanceAmount") {
      return Toastly.error(context, error);
    } else if (callingType == 'paymentStatus') {
      return Toastly.error(context, error);
    }
  }

  @override
  failure(failed, {callingType}) {
    if (callingType == "inititiateSocietyPayment") {
      setState(() {
        isLoading = false;
      });
      return Toastly.error(context, failed);
    } else if (callingType == "calulateTotalMaintenanceAmount") {
      return Toastly.error(context, failed);
    } else if (callingType == 'paymentStatus') {
      return Toastly.error(context, failed);
    }
  }

  @override
  ordererror(error, {callingType}) {
    throw UnimplementedError();
  }

  @override
  orderfailure(failed, {callingType}) {
    throw UnimplementedError();
  }

  @override
  ordersuccess(success, {callingType, String? searchedText}) {
    if (success != null) {
      setState(() {
        // billersData['order_id'] = success['data']['order_id'];
      });
    }
    payBill();
  }

  var orderId;

  paymentStatusPage(paymentStatus) {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => RentPaymentSuccussful(
                  isFromHistory: false,
                  comingFrom: FsString.MAINTENANCE,
                  paymentStatus: paymentStatus,
                  landLordAcDetails: widget.accountDetails,
                )));
  }

  @override
  success(success, {callingType, String? searchedText}) {
    if (callingType == "inititiateSocietyPayment") {
      if (success['data'] != null) {
        orderId = success['data']['order_id'];
        isLoading = false;
        Toastly.success(context, success['data']['message']);
        payBill(order_id: success['data']['order_id']);
      }
    } else if (callingType == "calulateTotalMaintenanceAmount") {
      if (success['data'] != null) {
        totalPayableAmt = success['data']['amount'];
      }
      setState(() {});
    } else if (callingType == 'paymentStatus') {
      if (success['data'] != null) {
        paymentStatusPage(success['data']);
        print('payment status::' + success['data'].toString());
      }
    }
  }

  /// Show authentication error dialog and handle relogin
  void _showAuthenticationError(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Authentication Error'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Clear authentication data
                TokenValidationService.clearAuthenticationData();
                // Navigate back to login
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/login',
                  (Route<dynamic> route) => false,
                );
              },
              child: const Text('Login Again'),
            ),
          ],
        );
      },
    );
  }
}
