import 'dart:collection';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cashfree_pg_sdk/api/cferrorresponse/cferrorresponse.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpayment/cfwebcheckoutpayment.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpaymentgateway/cfpaymentgatewayservice.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfsession/cfsession.dart';
import 'package:flutter_cashfree_pg_sdk/utils/cfenums.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:http/http.dart' as http;
import 'package:sso_futurescape/config/environment/environment.dart';
import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/main.dart';
import 'package:sso_futurescape/presentor/module/chsone/payment/onlinepaid/pay_online_staper_presentor.dart';
import 'package:sso_futurescape/ui/module/chsone/payment/onlinepaid/PayGatewayPage.dart';
import 'package:sso_futurescape/ui/module/chsone/payment/onlinepaid/pay_amount.dart';
import 'package:sso_futurescape/ui/module/chsone/payment/onlinepaid/pay_confirm.dart';
import 'package:sso_futurescape/ui/module/chsone/payment/onlinepaid/pay_ecollect.dart';
import 'package:sso_futurescape/ui/module/chsone/payment/onlinepaid/pay_method.dart';
import 'package:sso_futurescape/ui/module/chsone/payment/onlinepaid/transaction_success.dart';
import 'package:sso_futurescape/ui/widgets/stapper_widget/stapper_body.dart';
import 'package:sso_futurescape/ui/widgets/stapper_widget/stapper_main.dart';
import 'package:sso_futurescape/utils/auth/token_validation_service.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

// ignore: must_be_immutable
class PayOnlineStepper extends StatefulWidget {
  var currentUnit;
  var dueAmount;
  String? billType;

  PayOnlineStepper(this.currentUnit, {this.dueAmount, this.billType});

  @override
  State<StatefulWidget> createState() {
    return new PayOnlineStepperState(currentUnit, dueAmount: dueAmount);
  }
}

class PayOnlineStepperState
    extends State<PayOnlineStepper> /* implements PayOnlineStaperView*/ {
  late SFunction changes;
  PayOnlineStaperPresentor? payOnlineStaperPresentor;
  var currentUnit;
  var response;

  HashMap<String, String?>? paymentPayLoad;

  var dueAmount;

  var _selectedGateWay = {};

  PayOnlineStepperState(this.currentUnit, {this.dueAmount});

  @override
  void initState() {
    super.initState();

    print("this is selected $_selectedGateWay");
    payOnlineStaperPresentor = new PayOnlineStaperPresentor(this);
    if (kDebugMode) {
      print(currentUnit);
    }
    print("  -- ${widget.billType}");
    paymentPayLoad = new HashMap();
    paymentPayLoad!["soc_id"] = currentUnit["soc_id"].toString();
    paymentPayLoad!["unit_id"] = currentUnit["unit_id"].toString();
    paymentPayLoad!["source"] = "APPANDROID";
  }

  @override
  Widget build(BuildContext context) {
    return MyScrollView(
        isScrollable: false,
        pageTitle: "Payment",
        pageBody: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          child: StepperMainWidget(
            // controller: _myScrollController,
            enableStepClick: false,
            heading: false,
            onPageChange: (previous, current) {
              if (kDebugMode) {
                print(previous);
                print(current);
              }
            },
            title: "payment",
            afterInit: (SFunction change) {
              changes = change;
            },
            pages: [
              StapperBody(
                  title: "Already Paid".toLowerCase(),
                  description:
                      'Notify Complex Office about your Payment'.toLowerCase(),
                  child: Container(
                      child: PayAmountPage(
                          dueAmount: dueAmount,
                          onNext: (s) {
                            paymentPayLoad!["payment_amount"] =
                                s["payment_amount"];
                            paymentPayLoad!["paid_by"] = s["received_from"];
                            changes.next();
                          }))),
              new StapperBody(
                  title: "Already Paid".toLowerCase(),
                  description: 'select your payment gateway'.toLowerCase(),
                  child: Container(
                      child: PayGatewayPage(currentUnit, dueAmount: dueAmount,
                          onNext: (s) {
                    if (kDebugMode) {
                      print("hhhhhhhhhhhhhhhhhhhhhhh");
                      print(s);
                    }
                    _selectedGateWay["gateway"] = s;
                    /* paymentPayLoad["payment_amount"] = s["payment_amount"];
                        paymentPayLoad["paid_by"] = s["received_from"];*/
                    changes.next();
                  }))),
              new StapperBody(
                  title: "1",
                  description: "this is select your payment method",
                  child: Container(
                    child: PayMethodPage(currentUnit,
                        gateWays: _selectedGateWay, onNext: (s) {
                      paymentPayLoad!["mode"] = s["mode"];
                      paymentPayLoad!["payment_mode"] = s["payment_mode"];
                      paymentPayLoad!["pay_option"] = s["pay_option"];
                      paymentPayLoad!["vpa"] = s["vpa"];
                      if (s["pay_option"].toString() != "vpa") {
                        changes.next();
                      } else {
                        if (kDebugMode) {
                          print(paymentPayLoad);
                          print(s);
                        }
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => PayEcollectPage(s)),
                        );
                        /* Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => PayEcollectPage()),
                    );*/
                      }
                      if (kDebugMode) {
                        print(s);
                      }
                    }),
                  )),
              new StapperBody(
                  title: "1",
                  description: "select your payment method",
                  child: Container(
                    child: PayConfirmPage(
                      response,
                      paymentPayLoad,
                      gateWays: _selectedGateWay,
                      onNext: (s) {
                        print('Selected Gateway: $_selectedGateWay');

                        onPaymentInitiatedSuccessFully(
                            paymentPayLoad, _selectedGateWay);
                      },
                    ),
                  )),
            ],
          ),
        ));
  }

/*  @override
  void onPaymentError(String s) {}

  @override
  void onPaymentFailed(String s) {}

  @override
  void onPaymentSuccessFull(data) {}*/

  @override
  // ignore: override_on_non_overriding_member
  void onPaymentInitiatedSuccessFully(data, var selectedGateWay) async {
    try {
      // Validate authentication state before payment
      final validationResult =
          await TokenValidationService.validateAuthenticationState();
      if (!validationResult.isValid) {
        _showAuthenticationError(validationResult.message);
        return;
      }

      // Get fresh tokens for payment
      final freshTokens =
          await TokenValidationService.getFreshTokensForPayment();
      if (freshTokens == null) {
        _showAuthenticationError(
            "Unable to get valid authentication tokens. Please login again.");
        return;
      }

      var unitId = paymentPayLoad!["unit_id"];
      var socId = paymentPayLoad!["soc_id"];
      var amount = paymentPayLoad!["payment_amount"];
      var gayWay = paymentPayLoad!["payment_mode"];
      var chsonePaymentType = paymentPayLoad!["mode"];

      String? paymentUrls = Environment().getCurrentConfig().chsoneWebUrl +
          "payment/make-payment/gateway/$socId/$unitId?username=${freshTokens['username']}&session_token=${freshTokens['session_token']}" +
          "&amount=$amount&mode=$chsonePaymentType&gateway=$gayWay&access_token=${freshTokens['access_token']}&refresh_token=${freshTokens['refresh_token']}&bill_type=${widget.billType}&new_app=1";

      if (kDebugMode) {
        print("this is $paymentUrls");
      }

      if (selectedGateWay["gateway"]["gateway"] == "cashfreepg") {
        // Direct processing without loading dialog
        try {
          response = await http.get(Uri.parse(paymentUrls ?? ""));

          if (kDebugMode) {
            print('Response status: ${response.statusCode}');
            print('Response body: ${response.body}');
          }

          if (response.statusCode == 200) {
            try {
              // Debug: print the raw response body
              if (kDebugMode) {
                print('Raw response: ${response.body}');
              }

              // Parse JSON
              final data = jsonDecode(response.body);

              if (kDebugMode) {
                print('Response data: $data');
              }

              try {
                print("Starting payment process");
                var cfPaymentGatewayService = CFPaymentGatewayService();
                cfPaymentGatewayService.setCallback(verifyPayment, onError);

                var session = CFSessionBuilder()
                    .setEnvironment(CFEnvironment.PRODUCTION)
                    .setOrderId(data['data']['order_id'])
                    .setPaymentSessionId(data['data']['payment_session_id'])
                    .build();
                print("${session} this is session");

                var cfWebCheckout =
                    CFWebCheckoutPaymentBuilder().setSession(session).build();
                cfPaymentGatewayService.doPayment(cfWebCheckout);
              } catch (e) {
                print("Payment process error: ${e.toString()}");
              }
            } catch (e) {
              if (kDebugMode) {
                print('JSON parsing error: $e');
              }
            }
          } else {
            if (kDebugMode) {
              print('Request failed with status: ${response.statusCode}');
              print('Response body: ${response.body}');
            }
          }
        } finally {
          // Hide the loading dialog
          Navigator.pop(context);
        }
      } else {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => PaymentWebView(paymentUrls)),
        );
      }
    } catch (e) {
      // Handle authentication and payment errors
      print('Payment initiation error: $e');
      _showAuthenticationError("Payment initiation failed: ${e.toString()}");
    }
  }

  // void verifyPayment(String orderId) {

  //   debugPrint("Verify Payment");
  // }

  Future<void> verifyPayment(String orderId) async {
    print(" i am here ${widget.billType}");
    final url =
        'https://chsone.in/omnipaycashfreepayments/getOrderStatus?order_id=$orderId&bill_type=${widget.billType}';

    try {
      // Direct API call without loading dialog
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data.containsKey('status') && data.containsKey('message')) {
          int status = data['status'];
          String message = data['message'];
          int amount = data['amount'];

          print("${data} this is messages");

          // Direct navigation based on status
          if (status == 1) {
            await Fluttertoast.showToast(
              msg: "Transaction Successful - Amount: ₹$amount",
              toastLength: Toast.LENGTH_LONG,
              gravity: ToastGravity.BOTTOM,
              timeInSecForIosWeb: 5,
              backgroundColor: Colors.green,
              textColor: Colors.white,
              fontSize: 16.0,
            );
            // await Navigator.pushAndRemoveUntil(
            //   context,
            //   MaterialPageRoute(
            //     builder: (context) => OnesocietyHomepage(),
            //   ),
            //   (Route<dynamic> route) => false,
            // );
            await Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (context) => TransactionStatusCashfree(
                  status: status,
                  message: message,
                  amount: amount,
                ),
              ),
            );
          } else {
            await Fluttertoast.showToast(
              msg: "Transaction Failed - Please try again or contact support",
              toastLength: Toast.LENGTH_LONG,
              gravity: ToastGravity.BOTTOM,
              timeInSecForIosWeb: 5,
              backgroundColor: Colors.red.shade800,
              textColor: Colors.white,
              fontSize: 16.0,
            );
            // await Navigator.pushAndRemoveUntil(
            //   context,
            //   MaterialPageRoute(
            //     builder: (context) => OnesocietyHomepage(),
            //   ),
            //   (Route<dynamic> route) => false,
            // );
            await Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (context) => TransactionStatusCashfree(
                  status: status,
                  message: message,
                  amount: amount,
                ),
              ),
            );
          }
        } else {
          // Invalid response format - navigate to failure screen
          Fluttertoast.showToast(
            msg: "Invalid response format",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
            timeInSecForIosWeb: 1,
            backgroundColor: Colors.orange,
            textColor: Colors.white,
            fontSize: 16.0,
          );

          // Navigate to failure screen
          await Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => TransactionStatusCashfree(
                status: 0,
                message: "Invalid response format",
                amount: widget.dueAmount ?? "0",
              ),
            ),
          );
        }
      } else {
        // HTTP error - navigate to failure screen
        Fluttertoast.showToast(
          msg:
              "Failed to fetch order status. HTTP Code: ${response.statusCode}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.orange,
          textColor: Colors.white,
          fontSize: 16.0,
        );

        // Navigate to failure screen
        await Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => TransactionStatusCashfree(
              status: 0,
              message: "Failed to fetch order status",
              amount: widget.dueAmount ?? "0",
            ),
          ),
        );
      }
    } catch (e) {
      // Exception - navigate to failure screen
      Fluttertoast.showToast(
        msg: "Something went wrong",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.red,
        textColor: Colors.white,
        fontSize: 16.0,
      );

      // Navigate to failure screen
      await Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => TransactionStatusCashfree(
            status: 0,
            message: "Something went wrong: ${e.toString()}",
            amount: widget.dueAmount ?? "0",
          ),
        ),
      );
    }
  }

  // Removed loading dialog methods - using direct navigation instead

  void onError(CFErrorResponse errorResponse, String orderId) {
    print(errorResponse.getMessage());
    print("Error while making payment");
  }

  /// Show authentication error dialog and handle relogin
  void _showAuthenticationError(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Authentication Error'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate back to login
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/login',
                  (Route<dynamic> route) => false,
                );
              },
              child: const Text('Login Again'),
            ),
          ],
        );
      },
    );
  }
}

class PaymentWebView extends StatefulWidget {
  final String? paymentUrls;

  PaymentWebView(this.paymentUrls);

  @override
  State<StatefulWidget> createState() {
    return PaymentWebViewState();
  }
}

class PaymentWebViewState extends State<PaymentWebView> {
  late final WebViewController _webViewController;

  @override
  void initState() {
    super.initState();
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            if (kDebugMode) {
              print('WebView is loading (progress : $progress%)');
            }
          },
          onPageStarted: (String url) {
            if (kDebugMode) {
              print('Page started loading: $url');
            }
          },
          onUrlChange: (UrlChange url) {
            if (kDebugMode) {
              print('Url changed: ${url.url}');
            }
          },
          onPageFinished: (String url) {
            if (kDebugMode) {
              print('Page finished loading: $url');
            }
          },
          onWebResourceError: (WebResourceError error) {
            if (kDebugMode) {
              print('Error loading page: $error');
            }
          },
          onNavigationRequest: (NavigationRequest request) async {
            if (kDebugMode) {
              print('Navigating to: ${request.url}');
            }
            if (request.url.contains("chsone.in/dashboard")) {
              Navigator.pushReplacementNamed(context, '/dashboard');
              return NavigationDecision.prevent;
            } else if (request.url.startsWith("upi://pay?")) {
              if (await canLaunchUrl(
                Uri.parse(
                  request.url,
                ),
              )) {
                await launchUrl(
                  Uri.parse(
                    request.url,
                  ),
                );
                return NavigationDecision.prevent;
              }
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.paymentUrls ?? "b"));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Payment"),
        centerTitle: false,
        elevation: 1,
        iconTheme: IconThemeData(color: Colors.grey),
      ),
      body: WebViewWidget(controller: _webViewController),
    );
  }
}

/*
abstract class PayOnlineStaperView implements IPaymentHandlerOnline {}

abstract class IPaymentHandlerOnline implements IPaymentHandler {
  @override
  void onPaymentInitiatedSuccessFully(data);
}

class PayOnlineStaperPresentor {
  var payOnlineStaperView;

  PayOnlineStaperPresentor(this.payOnlineStaperView);

  void doOnlinePayment(HashMap<String, String> paymentPayLoad) {
    PaymentHandler handler =
    new PaymentHandler(PAYMENTTYPE.ONLINE, payOnlineStaperView);
    handler.doPayment(paymentPayLoad);
  }
}
*/
