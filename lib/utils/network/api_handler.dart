// ignore_for_file: deprecated_member_use_from_same_package, unused_local_variable

import 'dart:convert';

import 'package:common_config/utils/application.dart';
import 'package:common_config/utils/network/base_network.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:sso_futurescape/config/environment/environment.dart';
import 'package:sso_futurescape/utils/firebase_util/noomiKeys.dart';
import 'package:sso_futurescape/utils/storage/sso_storage.dart';

import '../../ui/module/login/ui/login.dart';
import '../auth/token_validation_service.dart';

class Network extends BaseNetwork {
  late NetworkHandler _networkHandler;
  var authonticator;
  static final int EXPIRED_OR_INVALID_ACCESS_TOKEN_RESPONSE_CODE = 1003;
  static final int LOGIN_CREADENTIALS_RESPONSE_CODE = 1000; //credentials
  static final int SQL_ERROR = 400; //credentials
  /*static final int INVALID_ACCESS_TOKEN_RESPONSE_CODE = 1003; //401;//1006;*/
  static BuildContext? context;

  Network(NetworkHandler _networkHandler) {
    this._networkHandler = _networkHandler;
    super.setHandler((code, response) {
      // print("responseresponseresponse");
      onSuccess(response);
      //print("responseresponseresponse");
    }, (code, response) {
      //print("onFailureonFailureonFailure");
      onFailure(response);
      // print("onFailureonFailure");
    }, (code, response) {
      // print("errorerrorerror");
      _networkHandler.error?.call(response);
      //  print("errorerrorerror");
    });
  }

  void onFailure(String? response) {
    //print('onFailure');
    //print(response);
    try {
      var profileJson = json.decode(response!);
      var status_code = profileJson['status_code'];
      print(status_code);
      if (status_code == EXPIRED_OR_INVALID_ACCESS_TOKEN_RESPONSE_CODE) {
        print('EXPIRED_OR_INVALID_ACCESS_TOKEN_RESPONSE_CODE');
        reGenerateAccessToken();
      } else {
        _networkHandler.failure?.call(response);
      }
    } catch (e) {
      _networkHandler.failure?.call(response);
    }
  }

  void onSuccess(String? response) {
    //var profileJson = json.decode(response);
    //print('onSuccess');
    //print(response);
    //print(profileJson['status_code']);
    _networkHandler.success?.call(response);
  }

  void excute() {
    print("    ndkfnsd");
    executeRequest();
  }

  void setHandlerX(NetworkHandler a) {}

  void setAuthonticator() {
    this.authonticator = authonticator;
  }

  void setErrorReporting(bool param0) {}

  successCallBack(tring) {
    _networkHandler.success?.call(tring);
  }

  void reGenerateAccessToken() {
    print("reGenerateAccessToken");

    // Use the new token validation service for refresh
    TokenValidationService.refreshTokenIfNeeded().then((refreshed) {
      if (refreshed) {
        print("Token refreshed successfully, retrying original request");
        // Retry the original request
        excute();
      } else {
        print("Token refresh failed, forcing relogin");
        relogin();
      }
    }).catchError((e) {
      print("Token refresh error: $e");
      relogin();
    });
  }

  void relogin() {
    print("Forcing relogin due to authentication failure");

    // Clear all authentication data using the new service
    TokenValidationService.clearAuthenticationData().then((_) {
      SsoStorage.getUserProfile().then((profile) {
        if (NoomiKeys.navKey.currentState != null) {
          // Check if profile is valid
          String? username = profile != null && profile.containsKey("username")
              ? profile["username"]
              : null;

          NoomiKeys.navKey.currentState!.pushAndRemoveUntil(
              MaterialPageRoute(
                  builder: (context) => Login(username: username)),
              (Route<dynamic> route) => false);
        }
      }).catchError((e) {
        print("Error getting user profile for relogin: $e");
        // Still try to navigate to login screen even if profile retrieval fails
        if (NoomiKeys.navKey.currentState != null) {
          NoomiKeys.navKey.currentState!.pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => Login()),
              (Route<dynamic> route) => false);
        }
      });
    }).catchError((e) {
      print("Error clearing authentication data: $e");
      // Force navigation to login even if clearing fails
      if (NoomiKeys.navKey.currentState != null) {
        NoomiKeys.navKey.currentState!.pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => Login()),
            (Route<dynamic> route) => false);
      }
    });
  }

  void excutePreviousRequest() {}

  /*@override
  String getErrorReortingURL() {
    return Environment().getCurrentConfig().ssoAuthUrl + "errors/logs";
  }*/

  @override
  String getErrorReportingUrl() {
    return Environment().getCurrentConfig().ssoAuthUrl + "errors/logs";
  }

  @override
  Future<dynamic> getDeviceInfo() async {
    var info;
    final DeviceInfoPlugin deviceInfoPlugin = new DeviceInfoPlugin();
    try {
      var currentPlatform = Environment().getCurrentPlatform();
      if (currentPlatform == FsPlatforms.ANDROID) {
        AndroidDeviceInfo info = await deviceInfoPlugin.androidInfo;
        //factory AndroidDeviceInfo.fromJson(Map<String, dynamic> json) => _$BreedFromJson(json);
        //Map<String, dynamic> toJson() => _$BreedToJson(this);
      } else if (currentPlatform == FsPlatforms.IOS) {
        info = await deviceInfoPlugin.iosInfo;
      }
    } catch (e) {
      info = {"platofme": "Web"};
    }

    //print(jsonEncode(info));
    return info.toString();
  }

  @override
  Future<dynamic> getUserProfile() {
    return SsoStorage.getUserProfile();
  }

  @override
  String getAppName() {
    return "CubeOneApp";
  }
}

class NetworkHandler {
  late void Function(String?)? success;
  late void Function(String?)? failure;
  late void Function(String?)? error;

  NetworkHandler(void Function(String?)? success,
      void Function(String?)? failure, void Function(String?)? error) {
    this.error = error;
    this.success = success;
    this.failure = failure;
  }
}
