import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:sso_futurescape/utils/storage/sso_storage.dart';
import 'package:sso_futurescape/utils/network/api_handler.dart';
import 'package:sso_futurescape/config/environment/environment.dart';
import 'package:sso_futurescape/utils/network/end_point.dart';

/// Comprehensive token validation and management service
class TokenValidationService {
  static const String _tag = 'TokenValidationService';

  /// Check if the current access token is expired
  static Future<bool> isAccessTokenExpired() async {
    try {
      final tokenStr = await SsoStorage.getToken();
      if (tokenStr == null || tokenStr.isEmpty) {
        log('$_tag: No token found', name: _tag);
        return true;
      }

      final tokenObj = jsonDecode(tokenStr);
      if (!tokenObj.containsKey('access_token')) {
        log('$_tag: Invalid token format', name: _tag);
        return true;
      }

      // For JWT tokens, we could decode and check exp claim
      // For now, we'll validate with server
      return await _validateTokenWithServer(tokenObj['access_token']);
    } catch (e) {
      log('$_tag: Error checking token expiry: $e', name: _tag);
      return true;
    }
  }

  /// Validate token with server
  static Future<bool> _validateTokenWithServer(String accessToken) async {
    try {
      // Simple validation - if we can get user profile, token is valid
      final profile = await SsoStorage.getUserProfile();
      return profile != null && profile.containsKey('username');
    } catch (e) {
      log('$_tag: Server validation failed: $e', name: _tag);
      return false;
    }
  }

  /// Refresh tokens if needed
  static Future<bool> refreshTokenIfNeeded() async {
    try {
      log('$_tag: Attempting token refresh', name: _tag);

      final tokenStr = await SsoStorage.getToken();
      if (tokenStr == null) {
        log('$_tag: No token to refresh', name: _tag);
        return false;
      }

      final tokenObj = jsonDecode(tokenStr);
      final refreshToken = tokenObj['refresh_token'];

      if (refreshToken == null || refreshToken.toString().isEmpty) {
        log('$_tag: No refresh token available', name: _tag);
        return false;
      }

      return await _performTokenRefresh(refreshToken);
    } catch (e) {
      log('$_tag: Token refresh failed: $e', name: _tag);
      return false;
    }
  }

  /// Perform actual token refresh
  static Future<bool> _performTokenRefresh(String refreshToken) async {
    try {
      final completer = Completer<bool>();

      final networkHandler = NetworkHandler(
        (success) async {
          try {
            final response = jsonDecode(success!);
            if (response != null && response.containsKey("data")) {
              // Save refreshed tokens
              SsoStorage.saveToken(jsonEncode(response["data"]));
              SsoStorage.setLogin("true");

              log('$_tag: Token refresh successful', name: _tag);
              completer.complete(true);
            } else {
              log('$_tag: Invalid refresh response format', name: _tag);
              completer.complete(false);
            }
          } catch (e) {
            log('$_tag: Error processing refresh response: $e', name: _tag);
            completer.complete(false);
          }
        },
        (failure) {
          log('$_tag: Token refresh failed: $failure', name: _tag);
          completer.complete(false);
        },
        (error) {
          log('$_tag: Token refresh error: $error', name: _tag);
          completer.complete(false);
        },
      );

      final hashMap = HashMap<String, String>();
      final currentConfig = Environment().getCurrentConfig();

      hashMap["api_key"] = currentConfig.ssoApiKey;
      hashMap["refresh_token"] = refreshToken;
      hashMap["source"] = "Chsone";
      hashMap["grant_type"] = "refresh_token";
      hashMap["auto_login"] = "1";
      hashMap["platform"] = "android";
      hashMap["client_id"] = currentConfig.ssoClientId;
      hashMap["client_secret"] = currentConfig.ssoClientSecret;

      final network = Network(networkHandler);
      network.setErrorReporting(true);
      network.setRequestDebug(true);
      network.postRequest(
        Environment().getCurrentConfig().ssoAuthUrl + Constant.USER_LOGIN,
        hashMap,
      );
      network.excute();

      return await completer.future.timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          log('$_tag: Token refresh timeout', name: _tag);
          return false;
        },
      );
    } catch (e) {
      log('$_tag: Token refresh exception: $e', name: _tag);
      return false;
    }
  }

  /// Validate authentication state before critical operations
  static Future<AuthValidationResult> validateAuthenticationState() async {
    try {
      log('$_tag: Validating authentication state', name: _tag);

      // Check if user is logged in
      final loginState = await SsoStorage.isLogin();
      if (loginState?.toLowerCase() != "true") {
        return AuthValidationResult.notLoggedIn();
      }

      // Check if tokens exist
      final tokenStr = await SsoStorage.getToken();
      if (tokenStr == null || tokenStr.isEmpty) {
        return AuthValidationResult.noTokens();
      }

      // Validate token format
      try {
        final tokenObj = jsonDecode(tokenStr);
        if (!tokenObj.containsKey('access_token') ||
            !tokenObj.containsKey('refresh_token')) {
          return AuthValidationResult.invalidTokenFormat();
        }
      } catch (e) {
        return AuthValidationResult.invalidTokenFormat();
      }

      // Check if token is expired
      final isExpired = await isAccessTokenExpired();
      if (isExpired) {
        log('$_tag: Token expired, attempting refresh', name: _tag);
        final refreshed = await refreshTokenIfNeeded();
        if (!refreshed) {
          return AuthValidationResult.refreshFailed();
        }
      }

      // Validate session token
      final profile = await SsoStorage.getUserProfile();
      if (profile == null || !profile.containsKey('session_token')) {
        return AuthValidationResult.noSessionToken();
      }

      log('$_tag: Authentication state valid', name: _tag);
      return AuthValidationResult.valid();
    } catch (e) {
      log('$_tag: Authentication validation error: $e', name: _tag);
      return AuthValidationResult.error(e.toString());
    }
  }

  /// Clear all authentication data
  static Future<void> clearAuthenticationData() async {
    try {
      log('$_tag: Clearing authentication data', name: _tag);

      SsoStorage.setLogin("false");

      // Clear tokens by setting them to empty values
      SsoStorage.saveToken("");
      SsoStorage.setRefreshToken("");
      SsoStorage.setSessionToken("");

      // Clear user profile data
      SsoStorage.setUserProfile(null);

      log('$_tag: Authentication data cleared', name: _tag);
    } catch (e) {
      log('$_tag: Error clearing authentication data: $e', name: _tag);
    }
  }

  /// Get fresh tokens for payment operations
  static Future<Map<String, String>?> getFreshTokensForPayment() async {
    try {
      final validationResult = await validateAuthenticationState();
      if (!validationResult.isValid) {
        log('$_tag: Authentication not valid for payment: ${validationResult.message}',
            name: _tag);
        return null;
      }

      final tokenStr = await SsoStorage.getToken();
      final tokenObj = jsonDecode(tokenStr!);
      final profile = await SsoStorage.getUserProfile();

      return {
        'access_token': tokenObj['access_token'],
        'refresh_token': tokenObj['refresh_token'],
        'session_token': profile!['session_token'],
        'username': profile['username'],
      };
    } catch (e) {
      log('$_tag: Error getting fresh tokens: $e', name: _tag);
      return null;
    }
  }
}

/// Result of authentication validation
class AuthValidationResult {
  final bool isValid;
  final String message;
  final AuthValidationError? error;

  AuthValidationResult._(this.isValid, this.message, this.error);

  factory AuthValidationResult.valid() =>
      AuthValidationResult._(true, 'Valid', null);
  factory AuthValidationResult.notLoggedIn() => AuthValidationResult._(
      false, 'User not logged in', AuthValidationError.notLoggedIn);
  factory AuthValidationResult.noTokens() => AuthValidationResult._(
      false, 'No authentication tokens found', AuthValidationError.noTokens);
  factory AuthValidationResult.invalidTokenFormat() => AuthValidationResult._(
      false, 'Invalid token format', AuthValidationError.invalidTokenFormat);
  factory AuthValidationResult.refreshFailed() => AuthValidationResult._(
      false, 'Token refresh failed', AuthValidationError.refreshFailed);
  factory AuthValidationResult.noSessionToken() => AuthValidationResult._(
      false, 'No session token found', AuthValidationError.noSessionToken);
  factory AuthValidationResult.error(String message) => AuthValidationResult._(
      false, 'Validation error: $message', AuthValidationError.unknown);
}

/// Types of authentication validation errors
enum AuthValidationError {
  notLoggedIn,
  noTokens,
  invalidTokenFormat,
  refreshFailed,
  noSessionToken,
  unknown,
}
