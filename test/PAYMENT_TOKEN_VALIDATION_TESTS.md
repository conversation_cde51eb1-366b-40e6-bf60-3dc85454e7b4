# Payment Token Validation Tests

This document describes the comprehensive test suite for the payment token validation fixes implemented to resolve the issue where payments fail until the user clears the app and logs in again.

## Overview

The test suite covers the following areas:
1. **Token Validation Service Tests** - Unit tests for the core token validation logic
2. **Payment Flow Integration Tests** - Integration tests for the complete payment flow
3. **Authentication State Management Tests** - Tests for session state synchronization

## Test Structure

### 1. Token Validation Service Tests (`test/utils/auth/token_validation_service_test.dart`)

#### Test Categories:

**isAccessTokenExpired Tests:**
- ✅ Returns true when no token exists
- ✅ Returns true when token is empty
- ✅ Returns true when token format is invalid
- ✅ Returns true when token missing access_token field

**validateAuthenticationState Tests:**
- ✅ Returns not logged in when login state is false
- ✅ Returns no tokens when token is null
- ✅ Returns invalid token format when token is malformed
- ✅ Returns no session token when profile missing session_token

**getFreshTokensForPayment Tests:**
- ✅ Returns null when authentication is invalid
- ✅ Returns tokens when authentication is valid

**clearAuthenticationData Tests:**
- ✅ Clears all authentication data properly

**AuthValidationResult Tests:**
- ✅ Creates valid result correctly
- ✅ Creates error results with proper error types
- ✅ Handles custom error messages

### 2. Payment Flow Integration Tests (`test/ui/module/chsone/payment/payment_flow_integration_test.dart`)

#### Test Categories:

**Token Validation Before Payment:**
- ✅ Shows authentication error when user not logged in
- ✅ Proceeds with payment when authentication is valid
- ✅ Shows error when tokens are invalid format

**Payment URL Construction:**
- ✅ Constructs payment URL with validated tokens
- ✅ Includes all required token parameters

**Error Handling:**
- ✅ Handles token refresh failure gracefully
- ✅ Clears authentication data on critical failure

**Session State Management:**
- ✅ Validates session token exists in profile
- ✅ Validates all required tokens are present

**Authentication State Persistence:**
- ✅ Maintains authentication state across app lifecycle

## Running the Tests

### Run All Payment Tests
```bash
flutter test test/run_payment_tests.dart
```

### Run Individual Test Files
```bash
# Token validation service tests
flutter test test/utils/auth/token_validation_service_test.dart

# Payment flow integration tests
flutter test test/ui/module/chsone/payment/payment_flow_integration_test.dart
```

### Run with Coverage
```bash
flutter test --coverage test/run_payment_tests.dart
genhtml coverage/lcov.info -o coverage/html
```

## Test Scenarios Covered

### 1. Authentication State Validation
- **Valid Authentication**: User logged in with valid tokens
- **Invalid Authentication**: User not logged in
- **Expired Tokens**: Tokens exist but are expired
- **Malformed Tokens**: Tokens exist but are corrupted
- **Missing Tokens**: No tokens available
- **Partial Tokens**: Some tokens missing (e.g., no refresh token)

### 2. Payment Flow Scenarios
- **Successful Payment Initiation**: Valid auth → payment proceeds
- **Authentication Failure**: Invalid auth → error dialog shown
- **Token Refresh**: Expired tokens → automatic refresh → payment proceeds
- **Refresh Failure**: Token refresh fails → relogin required

### 3. Error Handling Scenarios
- **Network Errors**: Handle network failures gracefully
- **Server Errors**: Handle 401/403 authentication errors
- **Token Corruption**: Handle corrupted token data
- **Session Mismatch**: Handle session token mismatches

### 4. User Experience Scenarios
- **Seamless Payment**: Valid auth → direct payment
- **Graceful Degradation**: Auth issues → clear error messages
- **Recovery Flow**: Auth failure → guided relogin process

## Expected Test Results

All tests should pass, indicating that:

1. **Token validation works correctly** - No more stale token issues
2. **Payment flows are secure** - Authentication is validated before payments
3. **Error handling is robust** - Users get clear feedback on auth issues
4. **Session management is reliable** - No more app restart requirements

## Test Coverage Goals

- **Unit Test Coverage**: >90% for TokenValidationService
- **Integration Test Coverage**: >80% for payment flows
- **Edge Case Coverage**: 100% for authentication error scenarios

## Continuous Integration

These tests should be run in CI/CD pipeline to ensure:
- No regression in payment functionality
- Authentication security is maintained
- User experience remains consistent

## Troubleshooting Test Failures

### Common Issues:

1. **Mock Setup Issues**: Ensure all SsoStorage methods are properly mocked
2. **Async Test Issues**: Use `tester.runAsync()` for async operations
3. **Widget Test Issues**: Ensure proper widget tree setup with MaterialApp

### Debug Commands:
```bash
# Run tests with verbose output
flutter test --verbose test/run_payment_tests.dart

# Run specific test group
flutter test --name "Token Validation Service Tests"

# Run with debug prints
flutter test --debug test/run_payment_tests.dart
```

## Future Test Enhancements

1. **Performance Tests**: Measure token validation performance
2. **Load Tests**: Test with multiple concurrent payment requests
3. **Security Tests**: Validate token encryption and storage security
4. **E2E Tests**: Full end-to-end payment flow testing
