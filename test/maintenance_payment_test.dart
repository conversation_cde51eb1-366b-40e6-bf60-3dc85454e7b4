import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Maintenance Payment UPI Tests', () {
    test('payBill should include UPI enabled options', () {
      // This test verifies that the payBill method creates options with UPI enabled
      final Map<String, dynamic> options = {
        'key': 'test_key',
        'order_id': 'test_order_id',
        'amount': 1000,
        'name': 'Test Society',
        'description': 'Society Maintenance',
        'options': {
          'checkout': {
            'method': {
              'netbanking': '1',
              'card': '1',
              'upi': '1',
              'wallet': '1'
            },
          },
        },
        'prefill': {
          'contact': '',
          'email': '',
        }
      };

      // Verify UPI is enabled
      final Map<String, dynamic> checkoutOptions =
          options['options'] as Map<String, dynamic>;
      final Map<String, dynamic> checkout =
          checkoutOptions['checkout'] as Map<String, dynamic>;
      final Map<String, dynamic> methods =
          checkout['method'] as Map<String, dynamic>;

      expect(methods['upi'], equals('1'));
      expect(methods['card'], equals('1'));
      expect(methods['netbanking'], equals('1'));
      expect(methods['wallet'], equals('1'));
    });
  });

  group('Payment Validation Tests', () {
    test('form validation should check required fields', () {
      final validData = {
        'amount': '1000',
        'pan_no': '**********',
        'note': 'Test payment',
      };

      final invalidAmountData = {
        'amount': '',
        'pan_no': '**********',
        'note': 'Test payment',
      };

      final invalidPanData = {
        'amount': '1000',
        'pan_no': 'INVALID',
        'note': 'Test payment',
      };

      // Test valid data structure
      expect(validData['amount'], isNotEmpty);
      expect(validData['pan_no'], isNotEmpty);
      expect(validData['note'], isNotEmpty);

      // Test invalid data structure
      expect(invalidAmountData['amount'], isEmpty);
      expect(invalidPanData['pan_no'], equals('INVALID'));
    });

    test('amount validation should check range', () {
      const validAmount = 5000;
      const tooLowAmount = 0;
      const tooHighAmount = 150000;

      expect(validAmount, greaterThan(0));
      expect(validAmount, lessThanOrEqualTo(100000));
      expect(tooLowAmount, equals(0));
      expect(tooHighAmount, greaterThan(100000));
    });

    test('PAN validation should check format', () {
      const validPan = '**********';
      const invalidPan = 'INVALID123';

      final panRegex =
          RegExp(r'^[A-Z]{3}[ABCFGHLJPTF]{1}[A-Z]{1}[0-9]{4}[A-Z]{1}$');

      expect(panRegex.hasMatch(validPan), isTrue);
      expect(panRegex.hasMatch(invalidPan), isFalse);
    });
  });
}
