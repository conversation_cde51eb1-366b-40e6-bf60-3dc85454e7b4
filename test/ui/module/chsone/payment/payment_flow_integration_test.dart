import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:sso_futurescape/utils/auth/token_validation_service.dart';
import 'package:sso_futurescape/utils/storage/sso_storage.dart';
import 'package:sso_futurescape/ui/module/chsone/payment/onlinepaid/pay_online_stepper.dart';

// Generate mocks
@GenerateMocks([SsoStorage])
import 'payment_flow_integration_test.mocks.dart';

void main() {
  group('Payment Flow Integration Tests', () {
    late MockSsoStorage mockSsoStorage;

    setUp(() {
      mockSsoStorage = MockSsoStorage();
    });

    group('Token Validation Before Payment', () {
      testWidgets('should show authentication error when user not logged in', (WidgetTester tester) async {
        // Arrange
        when(SsoStorage.isLogin()).thenAnswer((_) async => 'false');

        // Build widget
        await tester.pumpWidget(
          MaterialApp(
            home: PayOnlineStepper(
              paymentPayLoad: {
                'unit_id': '1',
                'soc_id': '1',
                'payment_amount': '1000',
                'payment_mode': 'online',
                'mode': 'maintenance'
              },
              billType: 'maintenance',
              dueAmount: '1000',
            ),
          ),
        );

        // Act - Trigger payment initiation
        final paymentState = tester.state<PayOnlineStepperState>(find.byType(PayOnlineStepper));
        
        // Simulate payment initiation with invalid auth
        await tester.runAsync(() async {
          paymentState.onPaymentInitiatedSuccessFully({}, {'gateway': {'gateway': 'razorpay'}});
        });

        await tester.pumpAndSettle();

        // Assert - Should show authentication error dialog
        expect(find.text('Authentication Error'), findsOneWidget);
        expect(find.text('User not logged in'), findsOneWidget);
        expect(find.text('Login Again'), findsOneWidget);
      });

      testWidgets('should proceed with payment when authentication is valid', (WidgetTester tester) async {
        // Arrange
        const validToken = '{"access_token": "test_access", "refresh_token": "test_refresh"}';
        const validProfile = {
          'username': 'test_user',
          'session_token': 'test_session'
        };
        
        when(SsoStorage.isLogin()).thenAnswer((_) async => 'true');
        when(SsoStorage.getToken()).thenAnswer((_) async => validToken);
        when(SsoStorage.getUserProfile()).thenAnswer((_) async => validProfile);

        // Build widget
        await tester.pumpWidget(
          MaterialApp(
            home: PayOnlineStepper(
              paymentPayLoad: {
                'unit_id': '1',
                'soc_id': '1',
                'payment_amount': '1000',
                'payment_mode': 'online',
                'mode': 'maintenance'
              },
              billType: 'maintenance',
              dueAmount: '1000',
            ),
          ),
        );

        // Act - Trigger payment initiation
        final paymentState = tester.state<PayOnlineStepperState>(find.byType(PayOnlineStepper));
        
        // Simulate payment initiation with valid auth
        await tester.runAsync(() async {
          paymentState.onPaymentInitiatedSuccessFully({}, {'gateway': {'gateway': 'razorpay'}});
        });

        await tester.pumpAndSettle();

        // Assert - Should not show authentication error
        expect(find.text('Authentication Error'), findsNothing);
      });

      testWidgets('should show error when tokens are invalid format', (WidgetTester tester) async {
        // Arrange
        when(SsoStorage.isLogin()).thenAnswer((_) async => 'true');
        when(SsoStorage.getToken()).thenAnswer((_) async => 'invalid_json');

        // Build widget
        await tester.pumpWidget(
          MaterialApp(
            home: PayOnlineStepper(
              paymentPayLoad: {
                'unit_id': '1',
                'soc_id': '1',
                'payment_amount': '1000',
                'payment_mode': 'online',
                'mode': 'maintenance'
              },
              billType: 'maintenance',
              dueAmount: '1000',
            ),
          ),
        );

        // Act - Trigger payment initiation
        final paymentState = tester.state<PayOnlineStepperState>(find.byType(PayOnlineStepper));
        
        await tester.runAsync(() async {
          paymentState.onPaymentInitiatedSuccessFully({}, {'gateway': {'gateway': 'razorpay'}});
        });

        await tester.pumpAndSettle();

        // Assert - Should show authentication error dialog
        expect(find.text('Authentication Error'), findsOneWidget);
        expect(find.text('Invalid token format'), findsOneWidget);
      });
    });

    group('Payment URL Construction', () {
      test('should construct payment URL with validated tokens', () async {
        // Arrange
        const validToken = '{"access_token": "test_access", "refresh_token": "test_refresh"}';
        const validProfile = {
          'username': 'test_user',
          'session_token': 'test_session'
        };
        
        when(SsoStorage.isLogin()).thenAnswer((_) async => 'true');
        when(SsoStorage.getToken()).thenAnswer((_) async => validToken);
        when(SsoStorage.getUserProfile()).thenAnswer((_) async => validProfile);

        // Act
        final tokens = await TokenValidationService.getFreshTokensForPayment();

        // Assert
        expect(tokens, isNotNull);
        expect(tokens!['access_token'], 'test_access');
        expect(tokens['refresh_token'], 'test_refresh');
        expect(tokens['session_token'], 'test_session');
        expect(tokens['username'], 'test_user');

        // Verify URL would be constructed with these tokens
        const expectedUrlPattern = 'username=test_user&session_token=test_session&access_token=test_access&refresh_token=test_refresh';
        expect(tokens.entries.map((e) => '${e.key}=${e.value}').join('&'), contains('username=test_user'));
        expect(tokens.entries.map((e) => '${e.key}=${e.value}').join('&'), contains('session_token=test_session'));
        expect(tokens.entries.map((e) => '${e.key}=${e.value}').join('&'), contains('access_token=test_access'));
        expect(tokens.entries.map((e) => '${e.key}=${e.value}').join('&'), contains('refresh_token=test_refresh'));
      });
    });

    group('Error Handling', () {
      test('should handle token refresh failure gracefully', () async {
        // Arrange
        when(SsoStorage.isLogin()).thenAnswer((_) async => 'true');
        when(SsoStorage.getToken()).thenAnswer((_) async => null);

        // Act
        final result = await TokenValidationService.validateAuthenticationState();

        // Assert
        expect(result.isValid, false);
        expect(result.error, AuthValidationError.noTokens);
      });

      test('should clear authentication data on critical failure', () async {
        // Act
        await TokenValidationService.clearAuthenticationData();

        // Assert
        verify(SsoStorage.setLogin('false')).called(1);
        verify(SsoStorage.saveToken('')).called(1);
        verify(SsoStorage.setRefreshToken('')).called(1);
        verify(SsoStorage.setSessionToken('')).called(1);
        verify(SsoStorage.setUserProfile(null)).called(1);
      });
    });

    group('Session State Management', () {
      test('should validate session token exists in profile', () async {
        // Arrange
        const validToken = '{"access_token": "test_access", "refresh_token": "test_refresh"}';
        const profileWithoutSession = {'username': 'test_user'};
        
        when(SsoStorage.isLogin()).thenAnswer((_) async => 'true');
        when(SsoStorage.getToken()).thenAnswer((_) async => validToken);
        when(SsoStorage.getUserProfile()).thenAnswer((_) async => profileWithoutSession);

        // Act
        final result = await TokenValidationService.validateAuthenticationState();

        // Assert
        expect(result.isValid, false);
        expect(result.error, AuthValidationError.noSessionToken);
        expect(result.message, 'No session token found');
      });

      test('should validate all required tokens are present', () async {
        // Arrange
        const incompleteToken = '{"access_token": "test_access"}'; // Missing refresh_token
        
        when(SsoStorage.isLogin()).thenAnswer((_) async => 'true');
        when(SsoStorage.getToken()).thenAnswer((_) async => incompleteToken);

        // Act
        final result = await TokenValidationService.validateAuthenticationState();

        // Assert
        expect(result.isValid, false);
        expect(result.error, AuthValidationError.invalidTokenFormat);
      });
    });

    group('Authentication State Persistence', () {
      test('should maintain authentication state across app lifecycle', () async {
        // Arrange
        const validToken = '{"access_token": "test_access", "refresh_token": "test_refresh"}';
        const validProfile = {
          'username': 'test_user',
          'session_token': 'test_session'
        };
        
        when(SsoStorage.isLogin()).thenAnswer((_) async => 'true');
        when(SsoStorage.getToken()).thenAnswer((_) async => validToken);
        when(SsoStorage.getUserProfile()).thenAnswer((_) async => validProfile);

        // Act - Multiple validation calls
        final result1 = await TokenValidationService.validateAuthenticationState();
        final result2 = await TokenValidationService.validateAuthenticationState();
        final tokens = await TokenValidationService.getFreshTokensForPayment();

        // Assert - All should be consistent
        expect(result1.isValid, true);
        expect(result2.isValid, true);
        expect(tokens, isNotNull);
        expect(tokens!['username'], 'test_user');
      });
    });
  });
}
