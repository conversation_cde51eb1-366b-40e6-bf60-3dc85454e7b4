import 'package:flutter_test/flutter_test.dart';
import 'utils/auth/token_validation_service_test.dart' as token_validation_tests;
import 'ui/module/chsone/payment/payment_flow_integration_test.dart' as payment_flow_tests;

/// Test runner for payment-related functionality
/// 
/// This script runs all tests related to the payment token validation fixes:
/// - Token validation service tests
/// - Payment flow integration tests
/// - Authentication state management tests
/// 
/// Usage:
/// ```bash
/// flutter test test/run_payment_tests.dart
/// ```
void main() {
  group('Payment Token Validation Test Suite', () {
    group('Token Validation Service Tests', () {
      token_validation_tests.main();
    });

    group('Payment Flow Integration Tests', () {
      payment_flow_tests.main();
    });
  });
}
