import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:sso_futurescape/utils/auth/token_validation_service.dart';
import 'package:sso_futurescape/utils/storage/sso_storage.dart';

// Generate mocks
@GenerateMocks([SsoStorage])
import 'token_validation_service_test.mocks.dart';

void main() {
  group('TokenValidationService', () {
    late MockSsoStorage mockSsoStorage;

    setUp(() {
      mockSsoStorage = MockSsoStorage();
    });

    group('isAccessTokenExpired', () {
      test('should return true when no token exists', () async {
        // Arrange
        when(SsoStorage.getToken()).thenAnswer((_) async => null);

        // Act
        final result = await TokenValidationService.isAccessTokenExpired();

        // Assert
        expect(result, true);
      });

      test('should return true when token is empty', () async {
        // Arrange
        when(SsoStorage.getToken()).thenAnswer((_) async => '');

        // Act
        final result = await TokenValidationService.isAccessTokenExpired();

        // Assert
        expect(result, true);
      });

      test('should return true when token format is invalid', () async {
        // Arrange
        when(SsoStorage.getToken()).thenAnswer((_) async => 'invalid_json');

        // Act
        final result = await TokenValidationService.isAccessTokenExpired();

        // Assert
        expect(result, true);
      });

      test('should return true when token missing access_token field', () async {
        // Arrange
        const tokenWithoutAccessToken = '{"refresh_token": "test_refresh"}';
        when(SsoStorage.getToken()).thenAnswer((_) async => tokenWithoutAccessToken);

        // Act
        final result = await TokenValidationService.isAccessTokenExpired();

        // Assert
        expect(result, true);
      });
    });

    group('validateAuthenticationState', () {
      test('should return not logged in when login state is false', () async {
        // Arrange
        when(SsoStorage.isLogin()).thenAnswer((_) async => 'false');

        // Act
        final result = await TokenValidationService.validateAuthenticationState();

        // Assert
        expect(result.isValid, false);
        expect(result.error, AuthValidationError.notLoggedIn);
        expect(result.message, 'User not logged in');
      });

      test('should return no tokens when token is null', () async {
        // Arrange
        when(SsoStorage.isLogin()).thenAnswer((_) async => 'true');
        when(SsoStorage.getToken()).thenAnswer((_) async => null);

        // Act
        final result = await TokenValidationService.validateAuthenticationState();

        // Assert
        expect(result.isValid, false);
        expect(result.error, AuthValidationError.noTokens);
        expect(result.message, 'No authentication tokens found');
      });

      test('should return invalid token format when token is malformed', () async {
        // Arrange
        when(SsoStorage.isLogin()).thenAnswer((_) async => 'true');
        when(SsoStorage.getToken()).thenAnswer((_) async => 'invalid_json');

        // Act
        final result = await TokenValidationService.validateAuthenticationState();

        // Assert
        expect(result.isValid, false);
        expect(result.error, AuthValidationError.invalidTokenFormat);
        expect(result.message, 'Invalid token format');
      });

      test('should return no session token when profile missing session_token', () async {
        // Arrange
        const validToken = '{"access_token": "test_access", "refresh_token": "test_refresh"}';
        const profileWithoutSession = {'username': 'test_user'};
        
        when(SsoStorage.isLogin()).thenAnswer((_) async => 'true');
        when(SsoStorage.getToken()).thenAnswer((_) async => validToken);
        when(SsoStorage.getUserProfile()).thenAnswer((_) async => profileWithoutSession);

        // Act
        final result = await TokenValidationService.validateAuthenticationState();

        // Assert
        expect(result.isValid, false);
        expect(result.error, AuthValidationError.noSessionToken);
        expect(result.message, 'No session token found');
      });
    });

    group('getFreshTokensForPayment', () {
      test('should return null when authentication is invalid', () async {
        // Arrange
        when(SsoStorage.isLogin()).thenAnswer((_) async => 'false');

        // Act
        final result = await TokenValidationService.getFreshTokensForPayment();

        // Assert
        expect(result, null);
      });

      test('should return tokens when authentication is valid', () async {
        // Arrange
        const validToken = '{"access_token": "test_access", "refresh_token": "test_refresh"}';
        const validProfile = {
          'username': 'test_user',
          'session_token': 'test_session'
        };
        
        when(SsoStorage.isLogin()).thenAnswer((_) async => 'true');
        when(SsoStorage.getToken()).thenAnswer((_) async => validToken);
        when(SsoStorage.getUserProfile()).thenAnswer((_) async => validProfile);

        // Act
        final result = await TokenValidationService.getFreshTokensForPayment();

        // Assert
        expect(result, isNotNull);
        expect(result!['access_token'], 'test_access');
        expect(result['refresh_token'], 'test_refresh');
        expect(result['session_token'], 'test_session');
        expect(result['username'], 'test_user');
      });
    });

    group('clearAuthenticationData', () {
      test('should clear all authentication data', () async {
        // Act
        await TokenValidationService.clearAuthenticationData();

        // Assert
        verify(SsoStorage.setLogin('false')).called(1);
        verify(SsoStorage.saveToken('')).called(1);
        verify(SsoStorage.setRefreshToken('')).called(1);
        verify(SsoStorage.setSessionToken('')).called(1);
        verify(SsoStorage.setUserProfile(null)).called(1);
      });
    });

    group('AuthValidationResult', () {
      test('should create valid result', () {
        // Act
        final result = AuthValidationResult.valid();

        // Assert
        expect(result.isValid, true);
        expect(result.message, 'Valid');
        expect(result.error, null);
      });

      test('should create not logged in result', () {
        // Act
        final result = AuthValidationResult.notLoggedIn();

        // Assert
        expect(result.isValid, false);
        expect(result.message, 'User not logged in');
        expect(result.error, AuthValidationError.notLoggedIn);
      });

      test('should create no tokens result', () {
        // Act
        final result = AuthValidationResult.noTokens();

        // Assert
        expect(result.isValid, false);
        expect(result.message, 'No authentication tokens found');
        expect(result.error, AuthValidationError.noTokens);
      });

      test('should create invalid token format result', () {
        // Act
        final result = AuthValidationResult.invalidTokenFormat();

        // Assert
        expect(result.isValid, false);
        expect(result.message, 'Invalid token format');
        expect(result.error, AuthValidationError.invalidTokenFormat);
      });

      test('should create refresh failed result', () {
        // Act
        final result = AuthValidationResult.refreshFailed();

        // Assert
        expect(result.isValid, false);
        expect(result.message, 'Token refresh failed');
        expect(result.error, AuthValidationError.refreshFailed);
      });

      test('should create no session token result', () {
        // Act
        final result = AuthValidationResult.noSessionToken();

        // Assert
        expect(result.isValid, false);
        expect(result.message, 'No session token found');
        expect(result.error, AuthValidationError.noSessionToken);
      });

      test('should create error result with custom message', () {
        // Act
        final result = AuthValidationResult.error('Custom error message');

        // Assert
        expect(result.isValid, false);
        expect(result.message, 'Validation error: Custom error message');
        expect(result.error, AuthValidationError.unknown);
      });
    });
  });
}
